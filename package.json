{"name": "root", "private": true, "maintainers": ["<PERSON><PERSON><PERSON><PERSON>"], "pofeVersion": "1.9.0", "scripts": {"prebootstrap": "node ./script/preinstall.js", "prepare": "husky install", "clean": "lerna clean", "test": "lerna exec -- yarn test", "lint": "yarn eslint --fix packages/components/lib --ext .js,.ts,.tsx", "bootstrap": "lerna bootstrap --force-local", "build": "npx lerna exec -- yarn build", "发布": "@npm", "release-ci": "lerna publish from-package --force-publish --no-git-reset --yes --loglevel 5  --registry https://registry-npm.wosai-inc.com/", "开发@wosai/emenu-mini-components#「微信」": "@久久折", "dev:components:wx:all": "npx lerna exec yarn dev:wx --scope=@wosai/emenu-mini-components", "开发@wosai/emenu-mini-components#「支付宝」": "@久久折", "dev:components:my:all": "npx lerna exec yarn dev:my --scope=@wosai/emenu-mini-components", "开发@wosai/emenu-mini-components-home#「微信」": "@收钱吧", "dev:components:wx:home": "npx lerna exec yarn dev:wx:home --scope=@wosai/emenu-mini-components", "build:components:wx:home": "npx lerna exec yarn build:wx:home --scope=@wosai/emenu-mini-components", "dev:components:my:home": "npx lerna exec yarn dev:my:home --scope=@wosai/emenu-mini-components", "dev:components:my:center": "npx lerna exec yarn dev:my:center --scope=@wosai/emenu-mini-components", "dev:components:my:order": "npx lerna exec yarn dev:my:order --scope=@wosai/emenu-mini-components", "开发@wosai/emenu-mini-components-order#「微信」": "@收钱吧", "build:components:wx:order": "npx lerna exec yarn build:wx:order --scope=@wosai/emenu-mini-components", "dev:components:wx:order": "npx lerna exec yarn dev:wx:order --scope=@wosai/emenu-mini-components", "dev:components:my:store": "npx lerna exec yarn dev:my:store --scope=@wosai/emenu-mini-components", "开发@wosai/emenu-mini-components-store#「微信」": "@收钱吧", "dev:components:wx:store": "npx lerna exec yarn dev:wx:store --scope=@wosai/emenu-mini-components", "开发@wosai/emenu-mini-components-center#「微信」": "@收钱吧", "dev:components:wx:center": "npx lerna exec yarn dev:wx:center --scope=@wosai/emenu-mini-components", "开发@wosai/emenu-mini-components-groupBuy#「微信」": "@收钱吧", "dev:components:wx:groupBuy": "npx lerna exec yarn dev:wx:groupBuy --scope=@wosai/emenu-mini-components", "build:wx:store": "npx lerna exec --scope=@wosai/emenu-mini-components yarn build:wx:store "}, "devDependencies": {"@babel/cli": "^7.14.8", "@babel/core": "^7.14.8", "@babel/eslint-parser": "^7.17.0", "@babel/node": "^7.14.7", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-decorators": "^7.14.5", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/plugin-transform-typescript": "^7.16.8", "@babel/preset-env": "^7.22.15", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@mini-types/alipay": "^2.0.0", "@rollup/plugin-commonjs": "^16.0.0", "@rollup/plugin-node-resolve": "^13.0.6", "@rollup/plugin-replace": "^4.0.0", "@swc/core": "^1.2.165", "@swc/jest": "^0.2.20", "@testing-library/dom": "^8.10.1", "@total-typescript/ts-reset": "^0.4.2", "@types/jest": "^27.4.1", "@types/lodash": "^4.14.182", "@types/wechat-miniprogram": "^3.4.1", "@typescript-eslint/eslint-plugin": "^5.26.0", "@typescript-eslint/parser": "^5.26.0", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "babel-plugin-transform-taroapi": "^3.0.16", "babel-preset-minify": "^0.5.1", "eslint": "^8.16.0", "flush-promises": "^1.0.2", "husky": "^7.0.4", "jest": "^27.5.1", "jest-localstorage-mock": "^2.4.3", "lerna": "^3.22.1", "less": "^4.1.1", "lint-staged": "^11.1.4", "miniprogram-automator": "^0.12.0", "miniprogram-simulate": "^1.6.1", "mockdate": "^3.0.2", "node-watch": "^0.7.3", "prettier": "^2.7.1", "pretty-quick": "^3.1.3", "rollup": "^2.33.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-json": "^4.0.0", "rollup-plugin-preserve-shebangs": "^0.2.0", "rollup-plugin-pug": "^1.1.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "scripty": "^2.1.1", "shelljs": "^0.8.5", "standard": "^16.0.2", "ts-jest": "^27.1.4", "typescript": "^4.7.2", "vite": "^3.1.1", "which": "1.3.1"}, "lint-staged": {"*": ["pretty-quick --staged"], "*.js": ["eslint --fix --ext .js,.jsx,.ts,.tsx", "git add"]}, "version": "1.4.8", "dependencies": {"dayjs": "^1.9.6", "enhanced-resolve": "^5.15.0", "lodash-miniprogram": "^4.17.11", "md5-node": "^1.0.1", "qs": "^6.9.4", "relative": "^3.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "wallaby": {"env": {"type": "node", "runner": "node"}, "testFramework": "bun"}}