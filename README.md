# @wmp-core

## 介绍
项目包含@wmp-core/utils, @wmp-core/services, @wmp-core/request等包，主要为久久折小程序封装各种常用包.

## 开始

### 准备

```
$lerna bootstrap
``` 

### Build

```
$yarn build
``` 

### Unit Test

```
$yarn test
``` 

### Lint

```
$yarn lint
```

### 发布

```
// 可以执行以下任个命令， 每个命令生成的版本号都不一样
yurn prepatch // => 1.4.38-alpha.0
yarn preminor  // 1.5.0-alpha.0
yarn premajor   // 2.0.0-alpha.0
yarn patch //   1.4.38
yarn minor  //  1.5.0
yarn  major  //  2.0.0
// 然后提交代码即CICD发布至npm
```

`
### 引入库

```
// 在@apps/h5使用h5组件
$lerna add @wmi-h5/components --scope=h5

// 在@apps/alipay使用alipay组件
$lerna add @wmi-alipay/components --scope=alipay

// 在@apps/weapp使用weapp组件
$lerna add @wmi-weapp/components --scope=weapp

// 外包npm包
$lerna add @tarojs/async-await --scope=app

## 开发新包

### 创建

```
 yarn create @wmp-core/request
```

### 给某个包增加某个包
```
lerna add qs --scope @wmp-core/request
```

### 发布
```
  yarn version
```

如果要配置jest、babel等，请参考其他包配置.

### 注意
* 本项目CI接入了UNIT TEST， SONA等
* 提交代码commit message严格按照 commitizen（https://www.conventionalcommits.org/en/v1.0.0/）

## 常见问题
- Current HEAD is already released
```
lerna publish from-package
```
- You must sign up for private packages

```json
// package.json 增加配置
  "publishConfig": {
    "access": "public"
  }
```
### 如何发正式版
当前， 如果在feature分支， 发布的都是alpha版本， 可用于开发使用. 如果需要发布正式版本，则需要MR到release/build分支， 并且打tag,即会自动打包成正式版本

## TODO
* CI接入自动发布至公司npm包资源平台
