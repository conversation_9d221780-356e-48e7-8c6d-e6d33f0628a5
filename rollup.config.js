// import pug from 'rollup-plugin-pug';
// import { terser } from 'rollup-plugin-terser';
const { terser } = require('rollup-plugin-terser');
const path = require('path');
const babel = require('rollup-plugin-babel');
// const buble = require('rollup-plugin-buble');
const json = require('rollup-plugin-json');
const typescript = require('rollup-plugin-typescript2');
const { preserveShebangs } = require('rollup-plugin-preserve-shebangs');
let lernJson = require('./lerna.json');
const commonjs = require('@rollup/plugin-commonjs');
const replace  = require('@rollup/plugin-replace');
// import nodeResolve from '@rollup/plugin-node-resolve';

const PACKAGE_ROOT_PATH = process.cwd();
const pkg = require(path.join(PACKAGE_ROOT_PATH, 'package.json'));
const INPUT_FILE = pkg.entry ? path.join(PACKAGE_ROOT_PATH, pkg.entry) : path.join(PACKAGE_ROOT_PATH, 'lib/index.js');

// const pkg = require(path.join(PACKAGE_ROOT_PATH, 'package.json'));

function createReplacePlugin() {
  const replacements = {
    __VERSION__: `"${lernJson.version}"`,
  };
  return replace({
    // @ts-ignore
    values: replacements,
    preventAssignment: true,
  });
}

const plugins = [
  // nodeResolve(),
  preserveShebangs(),
  json(),
  babel({
    babelrc: true,
    runtimeHelpers: true,
  }),
  commonjs(),
  createReplacePlugin(),
  typescript({
    verbosity: true,
    tsconfigOverride: {
      compilerOptions: {
        target: 'es6',
        allowJs: true,
        checkJs: true,
        moduleResolution: 'node',
        alwaysStrict: true,
        strictNullChecks: false,
        // emitDeclarationOnly: true,
        declaration: true,
        removeComments: false,
        ...(pkg.tsConfig || {}),
      },
      include: [`${PACKAGE_ROOT_PATH}/lib/**/*`],
      exclude: ['es', 'dist', '__tests__'],
    },
  }),
  // pug({ pugRuntime: false }),
  // buble({
  //   transforms: { forOf: false, asyncAwait: false },
  // }),
];

const output = [
  {
    file: 'dist/index.js',
    format: 'es', // cjs
    sourcemap: true,
  },
  // {
  //   file: pkg.module,
  //   format: 'es',
  //   sourcemap: true,
  // },
];

if (process.env.NODE_ENV === 'production') {
  plugins.push(terser());
  output.forEach((item) => (item.sourcemap = false));
}

module.exports = [
  {
    input: INPUT_FILE,
    output,
    plugins,
    external: [
      ...(pkg.external || []),
      // 'lodash-miniprogram',
      // '@tarojs/taro',
      // 'dayjs',
      // 'qs',
      // 'regenerator-runtime',
      // '@babel/runtime/helpers/typeof',
      // 'dayjs/plugin/isBetween',
    ],
    onwarn: function(message) {
      // Suppress this error message... there are hundreds of them. Angular team says to ignore it.
      // https://github.com/rollup/rollup/wiki/Troubleshooting#this-is-undefined
      if (/The 'this' keyword is equivalent to/.test(message)) {
        return;
      }
      if (/Error when using sourcemap for reporting an/.test(message)) {
        return true;
      }
      console.error(message);
    },
  },
];
