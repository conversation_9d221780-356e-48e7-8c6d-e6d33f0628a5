module.exports = {
  ignorePatterns: [
    'node_modules',
    'dist',
    'dist_npm_*',
    'script',
    '__tests__',
    'test',
    'miniPack',
    'lodash',
    'colorette.js',
    'components-home',
    'components-love',
    'components-order',
    'components-store',
  ],
  env: {
    browser: true,
    es6: true,
    node: true,
    jest: true,
  },
  globals: {
    Atomics: 'readonly',
    SharedArrayBuffer: 'readonly',
    getCurrentPages: true,
    my: true,
    wx: true,
    getApp: true,
    Component: true,
    Behavior: true,
    define: true,
    showToast: true,
    Page: true,
  },
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: 'module',
  },
  rules: {
    '@typescript-eslint/ban-ts-comment': 0,
    '@typescript-eslint/ban-types': 0,
    'no-unused-vars': 'off',
    'no-empty': 'warn',
    'no-sparse-arrays': 'warn',
    'no-fallthrough': 'warn',
    'no-prototype-builtins': 'warn',
    'no-control-regex': 'warn',
    'no-async-promise-executor': 'warn',
    'no-unexpected-multiline': 'warn',
    'no-useless-escape': 'warn',
    'no-redeclare': 'warn',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-this-alias': 'warn',
    '@typescript-eslint/no-empty-function': 'warn',
    '@typescript-eslint/no-unused-vars': ['warn'],
  },
  plugins: ['@typescript-eslint'],
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:@typescript-eslint/eslint-recommended',
    // 'plugin:@typescript-eslint/recommended-requiring-type-checking',
  ],
  parser: '@typescript-eslint/parser',
};
