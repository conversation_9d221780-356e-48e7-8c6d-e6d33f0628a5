<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>收钱吧服务协议</title>
    <style>
      html,
      body,
      div,
      span,
      applet,
      object,
      iframe,
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p,
      blockquote,
      pre,
      a,
      abbr,
      acronym,
      address,
      big,
      cite,
      code,
      del,
      dfn,
      em,
      img,
      ins,
      kbd,
      q,
      s,
      samp,
      small,
      strike,
      strong,
      sub,
      sup,
      tt,
      var,
      b,
      u,
      i,
      center,
      dl,
      dt,
      dd,
      ol,
      ul,
      li,
      fieldset,
      form,
      label,
      legend,
      table,
      caption,
      tbody,
      tfoot,
      thead,
      tr,
      th,
      td,
      article,
      aside,
      canvas,
      details,
      embed,
      figure,
      figcaption,
      footer,
      header,
      hgroup,
      menu,
      nav,
      output,
      ruby,
      section,
      summary,
      time,
      mark,
      audio,
      video {
        margin: 0;
        padding: 0;
        border: 0;
        font-size: 100%;
        font: inherit;
        vertical-align: baseline;
      }
      /* HTML5 display-role reset for older browsers */
      article,
      aside,
      details,
      figcaption,
      figure,
      footer,
      header,
      hgroup,
      menu,
      nav,
      section {
        display: block;
      }
      body {
        line-height: 1;
      }
      ol,
      ul {
        list-style: none;
      }
      blockquote,
      q {
        quotes: none;
      }
      blockquote:before,
      blockquote:after,
      q:before,
      q:after {
        content: '';
        content: none;
      }
      table {
        border-collapse: collapse;
        border-spacing: 0;
      }
      .center-x {
        display: flex;
        justify-content: center;
      }
      .center {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .header {
        display: flex;
        align-items: center;
        position: fixed;
        height: 1.2rem;
        width: 100%;
        /* padding-bottom: 0.36rem; */
        border-bottom: 1px solid #efefef;
        box-sizing: border-box;
        background: #fff;
      }
      .block {
        height: 1.2rem;
      }
      .tab {
        flex: 1;
        width: 50%;
        text-align: center;
        font-size: 0.3467rem;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-weight: 400;
        color: #9f9f9f;
        position: relative;
      }
      .tab1::before {
        content: '';
        position: absolute;
        width: 0.0133rem;
        height: 0.5867rem;
        /* border: .0133rem solid #D7D7D7; */
        background: #d7d7d7;
        right: 0;
      }
      .active {
        color: #000000;
        position: relative;
      }
      .active::after {
        position: absolute;
        content: '';
        width: 1.6rem;
        height: 0.1067rem;
        background: #000000;
        border-radius: 1.3333rem 1.3333rem 0px 0px;
        left: 50%;
        transform: translateX(-50%);
        bottom: -0.4rem;
      }
      .content {
        display: none;
        box-sizing: border-box;
        padding: 0.5333rem 0.4rem;
      }
      .content.show {
        display: block;
      }
      .title {
        font-size: 0.4267rem;
        font-family: PingFangSC-Medium, 'PingFang SC';
        font-weight: 500;
        color: #000000;
        line-height: 0.6rem;
        letter-spacing: 0.0133rem;
        text-align: center;
        margin-bottom: 0.4267rem;
      }
      .row {
        font-size: 0.3467rem;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-weight: 400;
        color: #000000;
        line-height: 0.4933rem;
      }
      .indent {
        text-indent: 0.5333rem;
      }
      .sub-title {
        font-weight: bolder;
        font-size: 0.3467rem;
        color: #000000;
        margin-top: 0.5333rem;
      }
      strong {
        color: #000000;
        font-weight: bolder;
      }
    </style>
    <script>
      (function flexible(window, document) {
        var docEl = document.documentElement;
        var dpr = window.devicePixelRatio || 1;

        // adjust body font size
        function setBodyFontSize() {
          if (document.body) {
            document.body.style.fontSize = 12 * dpr + 'px';
          } else {
            document.addEventListener('DOMContentLoaded', setBodyFontSize);
          }
        }
        setBodyFontSize();

        // set 1rem = viewWidth / 10
        function setRemUnit() {
          var rem = docEl.clientWidth / 10;
          docEl.style.fontSize = rem + 'px';
        }

        setRemUnit();

        // reset rem unit on page resize
        window.addEventListener('resize', setRemUnit);
        window.addEventListener('pageshow', function(e) {
          if (e.persisted) {
            setRemUnit();
          }
        });

        // detect 0.5px supports
        if (dpr >= 2) {
          var fakeBody = document.createElement('body');
          var testElement = document.createElement('div');
          testElement.style.border = '.5px solid transparent';
          fakeBody.appendChild(testElement);
          docEl.appendChild(fakeBody);
          if (testElement.offsetHeight === 1) {
            docEl.classList.add('hairlines');
          }
          docEl.removeChild(fakeBody);
        }
      })(window, document);
    </script>
  </head>
  <body>
    <div class="header">
      <div class="tab1 tab active" onclick="onSelect('tab1')">隐私政策</div>
      <div class="tab2 tab" onclick="onSelect('tab2')">服务协议</div>
    </div>
    <div class="block"></div>
    <div class="content tab1-content show">
      <header class="title">收钱吧消费者隐私保护政策</header>
      <div class="details">
        <p class="row">
          上海收钱吧互联网科技股份有限公司运营的收钱吧智慧门店消费者技术服务平台（以下
          简称“收钱吧平台”或者“我们”）提醒您在使用收钱吧平台之前，请务必仔细阅读本《隐 私政策》。
        </p>
        <p class="row indent">
          为向您提供更好的服务，在您使用收钱吧平台相关产品和服务的过程中，我们可能会收
          集和使用您提供的个人隐私信息，本隐私政策将详细说明您在使用我们服务时个人信息收集
          的范围，目的，收集方式，以及使用场景。
        </p>
        <h4 class="sub-title">一、我们如何收集和使用您的个人信息</h4>
        <p class="row">1、账号注册以及登录</p>
        <p class="row">
          当您注册以及登录时,我们会向您申请授权获得您本人的手机号码等。如果您不授权,将无法
          使用相关的功能。注:您的手机号码在您没有主动或同意交换的情况下,不会展示给其他用户。
        </p>
        <p class="row">2、在线支付</p>
        <p class="row">
          在您使用相关服务时,您可以选择第三方支付机构所提供的支付服务。支付功能中,我们并不
          收集您的个人信息,但依据法律和监管规定,我们需要将您的购买明细、订单号与交易金额信
          息与这些支付机构共享以实现其确认您的支付指令并完成支付。
        </p>
      </div>
      <p class="row">3、安全保障</p>
      <p class="row">
        为保障您的账户功能,我们将记录并存储用户在使用时的必要信息,在法律规定范围内自行决
        定单个用户在本软件及服务中数据的最长储存期限以及用户日志的储存期限，并在服务器上
        为其分配数据最大存储空间等。除法律法规规定的的情形外，未经用户的许可，我们不会向
        第三方公开、透露用户的个人信息。
      </p>
      <p class="row">4、个性化服务</p>
      <p class="row">
        为改善我们的产品或服务,以便向您提供更符合您个性化需求的信息展示、搜索及交易服务,
        我们会根据您的浏览及搜索记录、设备信息、位置信息、订单信息,提取您的浏览及搜索偏
        好、行为习惯、位置信息等特征,基于特征标签进行间接人群画像,并展示、推送信息和可能
        的商业广告信息。在前述过程中,我们会依法采取去标识化和匿名化等技术手段,以使得该等
        分析行为和结果,均不能重新识别或复原到您的个人身份信息。
      </p>
      <p class="row">1）若您提供的信息中包含第三方的个人信息,您需确保在您向我们提供前您已取得合法及有 效授权。</p>
      <p class="row">
        2）若我们将个人信息用于本《隐私政策》未载明的其他用途,或者将基于特定目的收集而来
        的信息用于其他目的,均会事先获得您的同意。
      </p>
      <p class="row">3）在以下情形中,我们在收集、使用您的个人信息无需事先征得您的授权同意:</p>
      <p class="row">(1)与我们履行法律法规规定的义务相关的;</p>
      <p class="row">(2)与国家安全、国防安全直接相关的;</p>
      <p class="row">(3)与公共安全、公共卫生、重大公共利益直接相关的;</p>
      <p class="row">(4)刑事侦查、起诉、审判和判决执行等直接相关的;</p>
      <p class="row">(5)出于维护您或其他个人的生命、财产等重大合法权益但又很难得到本人同意的；</p>
      <p class="row">(6)您自行向社会公众公开的个人信息;</p>
      <p class="row">(7)从合法公开披露的信息中收集个人信息的,如合法的新闻报道、政府信息公开等渠道。</p>
      <p class="row">(8)根据个人信息主体要求签订和履行合同所必需的;</p>
      <p class="row">(9)用于维护所提供的产品或服务的安全稳定运行所必需的,包括发现、处置产品或服务的故障;</p>
      <p class="row">
        (10)法律法规规定的其他情形。 请知悉,根据适用的法律,若我们对个人信息采取技术措施和其他必要措施进行处理,使得数
        据接收无法重新识别特定个人且不能复原,或我们可能对收集的信息进行去标识化地研究、
        统计分析和预测,用于改善收钱吧平台的内容和布局,为商业决策提供产品或服务支撑,以及
        改进我们的产品和服务(包括使用匿名数据进行机器学习或模型算法训练),则此类处理后的
        数据的使用无需另行向您通知并征得您的同意。
      </p>
      <h4 class="sub-title">二、我们如何共享、转让、公开披露您的个人信息</h4>
      <p class="row">
        我们不会向第三方共享、转让您的个人信息,除非经过您本人事先授权同意,或者共享、转让
        的个人信息是去标识化处理后的信息,且共享第三方无法重新识别此类信息的自然人主体。
      </p>
      <p class="row">1、共享</p>
      <p class="row">
        我们会以高度的勤勉义务对待您的信息。除以下情形外,未经您同意,我们不会与我们及必要
        的关联方以外的任何第三方公司、组织和个人分享您的信息:<br />
        当软件服务提供商或系统服务提供商与我们联合为您提供服务时,我们将向该等服务提供商
        共享您的个人信息。例如,当您需要使用地理位置功能时,为实现这一功能,我们可能会收集您
        的位置信息或相关设备信息(例如:IP地址、GPS位置、硬件型号、操作系统版本号、国际
        移动设备身份识别码(IMEI)、网络设备硬件地址(MAC)及IDFA),向提供定位和地图服务的服 务提供商共享。
      </p>
      <p class="row">
        1）为改善用户服务的目的,我们可能通过邮件、短信、电话等形式使用用户的个人信息,向用
        户提供可能感兴趣的信息,包括但不限于向用户发出产品和服务信息,或通过系统向用户展示
        个性化的第三方推广信息。在显示第三方推广信息时,我们会采取包括但不限于标记“广告”
        等形式提醒您注意,该等情况下,我们不会对外传递您的身份识别信息。如您点击第三方推广
        信息,将进入第三方提供的产品或服务页面,如您接受第三方的产品或服务,相应的权利义务
        由您与第三方具体约定。以上数据均不会透露给其它任何第三方。我们将适当地使用用户的 个人信息来实现我们的服务。
      </p>
      <p class="row">
        2）第三方服务。当您选择使用该第三方服务时,您授权我们将您的该信息提供给第三方服务
        平台或服务提供商(包括产品提供方、云服务提供商、短信服务提供商、配送服务提供方),
        以便其基于相关信息为您提供服务,或用于我们分析产品和服务使用情况,来提升您的使用体 验。
      </p>
      <p class="row">
        3）我们会对我们的服务使用情况进行统计,并可能会与公众或第三方分享这些统计信息,以展
        示我们的产品或服务的整体使用趋势。但这些统计信息不包含您的任何身份识别信息。
        我们承诺在共享过程中,尽最大可能保障您的数据和隐私不受侵害,并以不低于法律所要求的 保密和安全措施来处理该些信息。
      </p>
      <p class="row">2、转让</p>
      <p class="row">我们不会将您的个人信息转让给我们的关联公司外的任何公司、组织和个人,但以下情形除 外:</p>
      <p class="row">1）事先获得您的明确授权或同意;</p>
      <p class="row">2）满足法律法规、法律程序的要求或强制性的政府要求或司法裁定;</p>
      <p class="row">3、公开披露</p>
      <p class="row">我们仅会在以下情形下,公开披露您的个人信息:</p>
      <p class="row">1）获得您的明确同意;</p>
      <p class="row">2）基于法律法规、法律程序、诉讼或政府主管部门强制性要求下</p>
      <p class="row">4、例外情形</p>
      <p class="row">在以下情形中,共享、转让、公开披露您的个人信息无需事先征得您的授权同意:</p>
      <p class="row">1）与我们履行法律法规规定的义务相关的;</p>
      <p class="row">2）与国家安全、国防安全直接相关的;</p>
      <p class="row">3）与公共安全、公共卫生、重大公共利益直接相关的;</p>
      <p class="row">4）刑事侦查、起诉、审判和判决执行等直接相关的;</p>
      <p class="row">5）出于维护您或其他个人的生命、财产等重大合法权益但又很难得到本人同意的;</p>
      <p class="row">6）您自行向社会公众公开的个人信息;</p>
      <p class="row">7）从合法公开披露的信息中收集个人信息的,如合法的新闻报道、政府信息公开等渠道；</p>
      <p class="row">8）根据个人信息主体要求签订和履行合同所必需的；</p>
      <p class="row">9）用于维护所提供的产品或服务的安全稳定运行所必需的,包括发现、处置产品或服务的故 障；</p>
      <p class="row">10）法律法规规定的其他情形。</p>
      <p class="row">
        请知悉,根据适用的法律,若我们对个人信息采取技术措施和其他必要措施进行处理,使得数
        据接收方无法重新识别特定个人且不能复原,则此类处理后数据的共享、转让、公开披露无 需另行向您通知并征得您的同意。
      </p>
      <h4 class="sub-title">三、我们如何保存及保护您的个人信息</h4>
      <p class="row">1、保存信息</p>
      <p class="row">1）信息存储</p>
      <p class="row">
        您在使用我们的服务期间,我们将持续为您保存您的个人信息。如果您注销账户账号或主动
        删除上述信息,我们会及时删除您的个人信息或进行匿名化处理,因法律规定需要留存个人信
        息的,我们不会再将其用于日常业务活动中。
      </p>
      <p class="row">2）保存地域</p>
      <p class="row">
        我们承诺,您的个人信息将只存储在位于中华人民共和国境内的服务器上,您的信息不会被我
        们主动传输到境外。如在符合适用法律规定的情形下因业务需要向境外传输个人信息的,我
        们会事先征得您的同意,向您告知用户信息出境的目的、接收方、安全保障措施、安全风险 等情况,并依法开展安全评估。
      </p>
      <p class="row">3）例外情况</p>
      <p class="row">
        一般而言,我们仅为实现目的所必需的合理时间保留您的个人信息,但因法律、法规或生效司
        法文书等要求,或保护社会公共利益、其他合法权益所合理必须用途时,我们有可能因需符合
        法律要求,更改个人信息的存储时间。<br />
        当我们的服务发生停止运营的情形时,我们将按照法律规定向用户进行通知,通知方式包括但
        不限于消息推送、平台公告等,并在合理期限内删除或匿名化处理您的个人信息。
      </p>
      <p class="row">2、保护措施</p>
      <p class="row">
        1）我们已采取符合法律标准、合理可行的安全防护措施保护您的信息,防止个人信息遭到未
        经授权访问、公开披露、使用、修改、损坏或丢失。例如,我们会使用加密技术提高个人信
        息的安全性;我们会使用受信赖的保护机制防止个人信息遭到恶意攻击;我们会部署访问控制
        机制,尽力确保只有授权人员才可访问个人信息。
      </p>
      <p class="row">
        2）我们从组织建设、制度设计、人员管理、产品技术等方面多维度提升整个系统的安全性。
        目前,我们的重要信息系统已经通过网络安全等级保护的测评。
      </p>
      <p class="row">
        3）互联网并非绝对安全的环境,在您使用我们的服务时,请您妥善保护自己的个人信息,仅在
        必要的情形下向他人提供。如您发现自己的个人信息尤其是您的账号或密码发生泄露,请您
        立即与我们联系,以便我们根据您的申请采取相应措施。
      </p>
      <p class="row">3、安全事件通知</p>
    </div>
    <!-- 协议2 -->
    <div class="content tab2-content">
      <header class="title">
        收钱吧智慧门店<br />
        消费者技术服务协议
      </header>
      <div class="details">
        <p class="row" style="margin: bottom 40px;">欢迎使用收钱吧平台服务！</p>
        <p class="row">
          为保障您的合法权益，明确您（以下简称“您”、“用户”或者“消费者”）及上海收钱吧互
          联网科技股份有限公司（以下简称“收钱吧”在您使用收钱吧智慧门店消费者技术服务平台
          （以下简称“收钱吧平台”）过程中的各项权利和义务，由您和收钱吧就收钱吧平台的使用
          签署《收钱吧<strong>智慧门店消费者技术</strong>服务协议》（以下称“本协议”)。
        </p>
        <p class="row">
          您在同意本协议之前，应认真阅读、充分理解本协议各条款内容，特别是免除或限制责任的
          条款、争议解决的条款。免除或限制责任的条款及争议解决条款将以加粗方式标识，请您务
          必重点阅读。如果您对本协议的条款有疑问的，请拨打收钱吧客服热线(400--886-9999)进行 询问。
        </p>
        <p class="row">
          <strong
            >如果您是未满18周岁的未成年人或其他无民事行为、限制民事行为能力人，请在您的监护
            人指导下阅读并确认本协议。</strong
          >
        </p>
        <p class="row">
          <strong
            >您点击同意本协议或以其他方式确认本协议的，即表示您已阅读、理解并同意本协议各条
            款内容，本协议将成为您与收钱吧就收钱吧平台技术服务使用订立的有效合同，</strong
          >
          对您和收 钱吧产生法律约束力。如您不同意接受本协议的任一条款内容，或者无法准确理解相关条款
          含义的，请不要进行后续操作。
        </p>
        <h4 class="sub-title">
          第一条相关定义
        </h4>
        <p class="row">
          1、收钱吧智慧门店消费者技术服务平台：指收钱吧向您提供互联网信息服务的平台，包括
          微信小程序等。（以下简称“收钱吧平台”）
        </p>
        <p class="row">
          2、收钱吧智慧门店消费者技术服务：具体是指收钱吧通过平台向您提供的包括信息查询、
          商品购买、订单评价，储值技术服务，权益卡技术服务，优惠券技术服务，外卖配送技术服
          务，扫码点单技术服务等在内的各项技术服务。
        </p>
        <p class="row">
          3、商户：指与收钱吧签署《收钱吧智慧门店商户服务协议》，并在收钱吧平台开展各类经营 活动的主体。
        </p>
        <p class="row">4、关联公司：指与收钱吧存在一定程度控制关系的任何主体。</p>
        <h4 class="sub-title">
          第二条协议范围及适用
        </h4>
        <p class="row">
          1、您理解本协议无法涵盖您在使用收钱吧平台服务过程中的所有事项，因此我们会发布针
          对某些专门事项的单独规则或者补充协议，前述规则或补充协议与本协议具有同等法律效力。
        </p>
        <p class="row">
          2、您理解根据国家法律法规变化及维护交易秩序、保护消费者权益的需要，收钱吧会不时
          修改本协议。当我们修改协议时，我们将通过收钱吧平台进行公示，若您不同意协议修改的，
          您应在协议修改生效后停止使用我们提供的服务，若您在协议修改生效后继续使用我们的服
          务，则表示您同意修改后的协议。
        </p>
        <p class="row">
          3、用户通过收钱吧平台使用技术服务即适用本协议的相关约定，用户通过收钱吧平台使用
          由第三方提供的服务，例如收钱吧平台商户提供的服务、收钱吧开放平台开发者提供的服务
          等，受第三方服务提供者与用户之间确立的订单、协议等的约束，除另有约定外，本协议对
          用户和第三方服务提供者之间的具体服务关系不适用。
        </p>
        <p class="row">4、其他平台用户采用链接跳转方式进入收钱吧平台的，适用本协议。</p>
        <h4 class="sub-title">
          第三条用户资格及账号
        </h4>
        <p class="row">
          1、您在开始使用收钱吧服务前，应使用现有账号授权登陆收钱吧平台，若您拒绝授权或提 供，则您将无法使用收钱吧服务。
        </p>
        <p class="row">
          2、您授权使用您的当前账号登陆收钱吧平台，则表示您同意收钱吧获取您的账号相关信息，
          这些信息包括用户名、昵称、头像以及身份验证信息。
        </p>
        <h4 class="sub-title">
          第四条收钱吧智慧门店消费者技术服务类型
        </h4>
        <p class="row intent">
          收钱吧平台目前向用户提供如下服务，同时根据业务及技术发展情况，未来可能会提供
          更多的服务类型，同时，为了丰富用户可使用的服务种类，提升使用体验，收钱吧关联公司
          及第三方合作伙伴也会通过收钱吧平台向用户提供相应服务，具体而言，收钱吧提供的服务 主要包含以下几类：
        </p>
        <p class="row">
          1、信息查询服务：用户可通过收钱吧平台查询商户店铺信息、优惠信息及自己的交易信息，
          可通过收钱吧平台发布用户评价、查看由收钱吧根据地理位置向您推送的商户或商品信息。
        </p>
        <p class="row">2、储值技术服务：用户可通过收钱吧平台购买商户提供的储值服务等。</p>
        <p class="row">3、权益卡技术服务：用户可以通过收钱吧平台购买商户提供的权益卡服务等。</p>
        <p class="row">4、优惠券技术服务：用户可以通过收钱吧平台购买或者免费获得商户提供的优惠券服务等。</p>
        <p class="row">5、外卖技术服务：用户可以通过收钱吧平台购买餐饮商户提供的外卖服务。</p>
        <p class="row">6、扫码点单技术服务：用户可以通过收钱吧平台在商户店铺内进行手机点单服务等。</p>
        <p class="row">7、其他服务：收钱吧平台会不定期开展各种面向用户的活动，用户可选择是否参与</p>
        <h4 class="sub-title">
          第五条用户信息保护
        </h4>
        <p class="row">
          1、收钱吧非常重视用户个人信息保护，我们制定了详细的《收钱吧消费者隐私保护政策》，
          我们将按照公示的政策及相关法律法规的要求，对您的个人相关信息予以保护。您同意收钱
          吧按照《收钱吧消费者隐私保护政策》收集、存储、使用、披露和保护您的个人信息。收钱
          吧希望通过隐私权政策向您清楚地介绍收钱吧对您个人信息的处理方式，因此收钱吧建议您
          完整地阅读《收钱吧消费者隐私保护政策》，以帮助您更好地保护您的个人信息。
        </p>
        <p class="row">
          2、收钱吧将遵循合法、必要、合理的原则收集、存储、使用、共享和保护您的个人信息，
          除《收钱吧消费者隐私保护政策》已明确和公示的内容外，我们不会再收集您的个人信息，
          但若产品或服务更新、相关法律法规要求，则我们会在遵循前述原则的情况下使用或者收集 您的个人信息。
        </p>
        <p class="row">
          3、我们会在遵循《收钱吧消费者隐私保护政策》及相关法律法规的前提下,使用或者收集用
          户个人信息,但为了公共利益或其他符合相关法律法规的要求下,我们会在数据脱敏、去标识
          化、以及无法识别个人身份信息后的数据做大数据分析、数学建模等使用,您理解此种使用 无需征得您的同意或授权。
        </p>
        <h4 class="sub-title">
          第六条用户的权利和义务
        </h4>
        <p class="row">
          1、您应使用本人的账号登陆收钱吧平台,并妥善保管账号和密码,通过您的账号进行的行为都
          视为您本人的行为,由您承担相应的法律后果。
        </p>
        <p class="row">
          <strong
            >2、您理解,商户在收钱吧平台发布的优惠商品、券、红包等数量是有限的,您可以购买的数
            量可能会受到限制,请在购买或领取时按照限购或限领数量进行,且您不得采用注册多个账
            户、采用技术手段或其他作弊手段获取超过数量的优惠，否则商户有权拒绝你使用或取消 您已获得的优惠资格。</strong
          >
        </p>
        <p class="row">
          <strong
            >3、您理解，平台内的各项活动，均是面向个人消费者的，各类活动均有规则，您应当遵守
            相应的规则，否则商户或者收钱吧取消您参与相关活动的资格,收钱吧对您的账号进行冻结、
            限权、禁用等处置措施,以保障其他用户公平参与活动的权利。</strong
          >
        </p>
        <p class="row">
          4、您应出于真实消费目的领取或购买优惠凭证,不得转售或扰乱平台秩序,否则商户或者收钱
          吧可以将您领取的优惠凭证作废或取消您购买优惠凭证的订单。同时,您应特别注意优惠凭
          证的有效期、预约时间及退款条件,按优惠凭证使用条件使用。
        </p>
        <p class="row">
          5、您不得以任何技术手段或其他方式干扰收钱吧平台的正常运行或干扰其他用户对收钱吧
          平台服务的使用。不得利用技术手段或其他任何方式获取不正当利益。
        </p>
        <p class="row">
          6、如您要求获得优惠凭证对应商品/服务的发票、其他付款凭证、购货凭证或服务单据,您
          应向发布或提供该优惠凭证的商户提出,发票金额以您实际支付的价款为准。<strong
            >您理解,收钱吧 仅是提供互联网信息服务的平台,不是您交易的相对方,不承担销售者责任,无义务向您开具 发票。
          </strong>
        </p>
        <p class="row">
          7、您不得进行虚假评价或虚假投诉;您的评价内容不得违反任何法律、法规、条例或规章,
          以及相关行业性规定,不含有任何违反国家法律、危害国家安全统一、社会稳定、公序良俗、
          社会公德以及淫秽、色情、不道德、欺诈、诽谤(包括商业诽谤)、恶意竞争、非法恐吓或非
          法骚扰的内容;否则收钱吧有权随时予以删除。如因您发布的评论给商户、其他用户或收钱
          吧造成损失的,由您自行承担赔偿责任。
        </p>
        <p class="row">
          8、您不得利用技术手段反编译、破解、破坏收钱吧平台相关软件及系统，不得未经收钱吧
          同意将收钱吧平台相应内容，包括但不限于优惠信息、评价信息、产品界面等复制、转发或
          采用其他手段用于收钱吧平台外的用途或目的。
        </p>
        <p class="row">9、您不得以虚构事实、隐瞒真相、编造不实信息等方式恶意诋毁收钱吧、收钱吧平台或商 户的商誉。</p>
        <p class="row">
          10、您不得利用收钱吧所提供的服务进行与使用该等服务无关的行为或其他违法违规及损害
          他人合法权益的行为，包括但不限于妨害他人名誉或隐私权；或以自己名义、匿名或冒用他
          人或收钱吧名义散播诽谤、不实、威胁、不雅、猥亵、不法、攻击性或侵害他人权利的消息
          或文字，传播色情或其它违反社会公德的言论；传输或散布计算机病毒；从事广告或贩卖商
          品；从事不法交易或张贴虚假不实或引人犯罪的信息；或任何违反中华人民共和国法律或其 它法令的行为。
        </p>
        <p class="row">
          11、除了遵守本协议约定的内容外，您还需要遵守收钱吧平台不时公布的其他服务条款和规
          则，包括但不限于优惠凭证领取规则、使用规则等。
        </p>
        <h4 class="sub-title">
          第七条收钱吧的权利和义务
        </h4>
        <p class="row">
          1、收钱吧将按照本协议约定的服务内容，向您提供相应的技术服务。您理解并同意收钱吧
          提供的服务仅为技术服务，且是基于收钱吧现有能力而提供，我们不保证提供的服务没有瑕
          疵，但我们会努力提升我们的服务能力，改善我们的服务体验。
        </p>
        <p class="row">
          2、为了向您提供高质量的服务,收钱吧有可能对收钱吧平台系统进行相应的改造、升级或调
          整,开发新的产品、功能、模块,设计新的使用场景,对平台产品进行升级迭代等。
        </p>
        <p class="row">
          3、为保证平台经营秩序,保证所有用户公平使用的权利,收钱吧有权制定相应的平台规则并通
          过收钱吧平台予以公示,您若不同意相关规则的,应停止使用相关服务,若您使用相关服务,即 表示您同意相关规则。
        </p>
        <p class="row">
          4、收钱吧有权自主组织收钱吧平台的各项运营活动,设定相应的运营活动规则,收钱吧不能保
          证所有的活动您均可以参加或享受,您是否可以参与相应活动均以收钱吧平台相应活动规则 为准。
        </p>
        <p class="row">
          5、收钱吧对收钱吧平台所有产品和内容享有相应的知识产权和其他权利,具体以知识产权条
          款及收钱吧知识产权声明所述内容为准。
        </p>
        <p class="row">
          6、收钱吧对收钱吧平台上产生的所有数据享有合法权益,有权在合法合规和符合隐私政策的
          前提下对相关数据进行使用,有权基于数据开发新的产品并由收钱吧享有完全独立的权利。
        </p>
        <p class="row">
          <strong
            >7、平台中的服务并非单独由收钱吧平台经营主体提供,收钱吧关联公司和其他第三方主体会
            通过收钱吧平台向用户提供相关服务,对于收钱吧关联公司及第三方主体向用户提供的服务,
            将由这些主体向用户履行义务并承担相应的责任。</strong
          >
        </p>
        <p class="row">
          8、收钱吧平台经营主体在某些情形下可能会发生变更，在收钱吧平台经营者发生变更时，
          收钱吧会通过平台公告的方式向用户公示，若用户不同意变更的，应停止使用收钱吧服务，
          若用户继续使用收钱吧服务的，表示用户对收钱吧经营主体变更无异议。在收钱吧经营主体
          发生变更的情形下，除非您明确表示您不再使用收钱吧服务，否则我们会将您使用收钱吧平
          台的相关权益一并移交给新的经营主体，由新的主体继续向您提供服务。
        </p>
        <h4 class="sub-title">
          第八条违约责任
        </h4>
        <p class="row">
          1、用户违反本协议相关约定，收钱吧有权按照本协议相关约定对用户采取冻结账号、暂停
          服务、限制使用等处置措施，本协议另有约定的，从其约定。
        </p>
        <p class="row">2、本协议任意一方违反约定给另一方造成损失的，应赔偿对方因违约行为而遭受的损失。</p>
        <p class="row">
          3、用户违反本协议约定，采取技术或其他手段套取平台营销补贴或获取其他不正当利益的，
          收钱吧有权要求用户返还相应金额，构成犯罪的，收钱吧有权采取向公安机关举报，由司法
          机关依法追究其刑事责任的方式来维护自身权益。
        </p>
        <p class="row">
          4、用户应向收钱吧支付的赔偿或其他款项，用户若未予以支付，用户同意收钱吧通过合法
          途径自用户支付宝账户或其他收钱吧可控制的账户中扣除。
        </p>
        <h4 class="sub-title">
          第九条知识产权
        </h4>
        <p class="row">
          1、收钱吧平台所包含的全部智力成果包括但不限于数据库、网站设计、文字和图表、软件、
          照片、录音录像、音乐、声音及其前述组合,软件编译、相关源代码和软件(包括小应用程序
          和脚本)的知识产权权利均归收钱吧所有。未经许可您不得使用前述任何内容。
        </p>
        <p class="row">
          <strong
            >2、用户理解并同意,在使用收钱吧服务时上传、发布的文字、图片、视频、音频、软件以及
            表演等(以下简称“用户上传、发布的内容”)为用户原创或已获合法授权。由用户通过收钱
            吧平台所上传、发布的所有内容的知识产权归属用户或原始著作权人所有。但用户同意在
            全世界范围内,永久性的、不可撤销的、免费的授予收钱吧平台及其关联公司独占的对用户
            上传、发布的内容进行存储、使用、发布、复制、出租、展览、展示、放映、广播、信息
            网络传播、摄制、修改、改编、汇编、出版、翻译、据以创作衍生作品、传播、表演和转
            授权的权利。用户同时授权收钱吧平台及其关联公司有权以其自己的名义单独或委托专业
            第三方对侵犯以上内容著作权的行为进行维权,维权形式包括但不限于：监测侵权行为、发
            送维权函、提起诉讼或仲裁、调解、和解并获得损害赔偿等任何方式。</strong
          >
        </p>
        <h4 class="sub-title">
          第十条责任限制
        </h4>
        <p class="row">
          1、收钱吧平台因下列状况无法正常运作,使您无法使用各项服务时,收钱吧不承担损害赔偿责
          任,该状况包括但不限于:收钱吧平台在本网站或平台公告之系统停机维护期间;电信设备出
          现故障不能进行数据传输的;因台风、地震、海啸、洪水、停电、战争、恐怖袭击等不可抗
          力之因素,造成本公司系统障碍不能执行业务的;由于黑客攻击、电信、电力部门技术调整或
          故障、网站升级、银行方面的问题等原因而造成的服务中断或者延迟。
        </p>
        <p class="row">
          <strong
            >2、您理解并同意,对商户提供的商品或服务,及您使用优惠凭证在商户消费过程中产生的体
            验感受、服务品质等,均由商户负责,收钱吧不负有任何明示或默示的担保责任。</strong
          >如您与商户 因优惠凭证的使用而发生任何争议,包括但不限于商户提供的商品或服务的数量、质量、价
          格、有效期、预约时间、商户地址、退货退款、售后服务、服务态度等问题发生争议的,您
          应与商户采取协商或其他方式予以解决。
        </p>
        <h4 class="sub-title">
          第十一条通知
        </h4>
        <p class="row">
          1、本协议履行过程中，收钱吧向您发出的书面通知方式包括但不限于邮寄纸质通知、网站
          公告、电子邮件、站内信、旺旺系统信息、手机短信和传真等。
        </p>
        <p class="row">
          2、您同意，为了您正常使用收钱吧平台的相关服务，收钱吧将向您发送与收钱吧平台服务
          相关的电子邮件或短信息，包括但不限于优惠服务信息、收钱吧平台系统调整或升级通知、
          您在收钱吧平台上领取优惠凭证的状态相关的通知等。
        </p>
        <h4 class="sub-title">
          第十二条争议解决
        </h4>
        <p class="row">
          您与收钱吧因本协议的履行发生争议的，应通过友好协商解决，协商不成的，任一方有权将
          争议提交收钱吧住所地人民法院进行诉讼。
        </p>
        <h4 class="sub-title">
          第十三条协议终止
        </h4>
        <p class="row">1、您有权通过以下任意一种方式终止本协议：</p>
        <p class="row">1）您有权通过注销登录账户的方式终止本协议，当您注销登录所使用的账号时，本协议终 止；</p>
        <p class="row">2）本协议发生变更时，你以收钱吧提供的渠道明示不同意变更事项的，本协议自您明示不 同意时终止；</p>
        <p class="row">
          2、您同意，出现以下任一情形的，收钱吧有权单方中止或终止本协议的部分或全部，且不 需要承担任何责任：
        </p>
        <p class="row">
          1）如您违反了本协议约定的禁止性规定（禁止性规定，是指协议条款中含有“不得”、“不
          能”等表述的内容，不可抗力条款中的表述除外），收钱吧有权终止本协议：
        </p>
        <p class="row">2）国家法律法规或政策发生变化的；</p>
        <p class="row">
          3)收钱吧自身业务情况发生重大变化时，收钱吧有权以公告方式公告终止本协议，但会按
          照法律法规规定提前通过平台公告的方式通知您。
        </p>
        <p class="row">
          3、本协议终止后，除非法律有明确规定，否则收钱吧无义务向您或您指定的第三方披露您
          账户中的任何信息。但依照相关法律法规的规定，收钱吧可以继续留存您的信息。
        </p>
        <p class="row">
          4、本协议终止不影响您在协议有效期间违约而应承担的责任，收钱吧仍有权就您过往的违 约行为依照本协议向您主张权利。
        </p>
        <p class="row">
          5、本协议终止后，您在协议有效期间已提交的订单，收钱吧有权通知交易相对方决定是否
          继续履行该等订单，若交易相对方要求继续履行的，您应继续予以履行并承担相应的费用及
          责任或损失，若交易相对方不要求继续履行，则该等订单将予以关闭，您无权就关闭的订单
          向收钱吧或交易相对方主张任何权利。
        </p>
      </div>
    </div>
    <script>
      function hasClass(ele, cls) {
        cls = cls || '';
        if (cls.replace(/\s/g, '').length == 0) return false; //当cls没有参数时，返回false
        return new RegExp(' ' + cls + ' ').test(' ' + ele.className + ' ');
      }

      function addClass(ele, cls) {
        if (!hasClass(ele, cls)) {
          ele.className = ele.className == '' ? cls : ele.className + ' ' + cls;
        }
      }

      function removeClass(ele, cls) {
        if (hasClass(ele, cls)) {
          var newClass = ' ' + ele.className.replace(/[\t\r\n]/g, '') + ' ';
          while (newClass.indexOf(' ' + cls + ' ') >= 0) {
            newClass = newClass.replace(' ' + cls + ' ', ' ');
          }
          ele.className = newClass.replace(/^\s+|\s+$/g, '');
        }
      }
      function siblings(elm) {
        var a = [];
        var p = elm.parentNode.children;
        for (var i = 0, pl = p.length; i < pl; i++) {
          if (p[i] !== elm) a.push(p[i]);
        }
        return a;
      }

      const onSelect = (val) => {
        const target = document.getElementsByClassName(val)[0];
        const other = siblings(target) || [];
        other.forEach((item) => removeClass(item, 'active'));
        addClass(target, 'active');
        const showContentTarget = document.getElementsByClassName(`${val}-content`)[0];
        const otherContent = siblings(showContentTarget) || [];
        otherContent.forEach((item) => removeClass(item, 'show'));
        addClass(showContentTarget, 'show');
      };
    </script>
  </body>
</html>
