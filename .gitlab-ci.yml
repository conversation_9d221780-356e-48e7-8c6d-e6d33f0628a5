stages:
  - deploy
cache:
  paths:
    - node_modules
before_script:
  - export NODE_OPTIONS=--max-old-space-size=32768
#  - npm config set registry https://registry-npm.wosai-inc.com
  - npm install --no-package-lock
  - npx lerna clean -y
  - npm run bootstrap --ci --private
  - cat .npmrc
  - yarn build
  - git config --global user.name "${GITLAB_USER_NAME}"
  - git config --global user.email "${GITLAB_USER_EMAIL}"
  - git config --global core.warnambiguousrefs false
  - git remote set-url origin "https://gitlab-ci-token:$GIT_ACCESS_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH.git"

deploy-feature:
  stage: deploy
  script:
    - COMMIT_MESSAGE=`echo $(git log -1 --pretty=%B)`
    - echo $CI_COMMIT_AUTHOR
    - echo $CI_COMMIT_TITLE
    - echo $CI_COMMIT_MESSAGE
    - echo $CI_COMMIT_DESCRIPTION
    - echo $CI_JOB_URL
    - echo $CI_MERGE_REQUEST_DIFF_ID
    - echo $CI_COMMIT_BRANCH
    - git checkout -B "$CI_COMMIT_REF_NAME" "$CI_COMMIT_SHA"
    - npx lerna version prepatch --force-publish  -y --no-git-tag-version --conventional-commits
    - git add .
    - git commit -m "bump new beta version"
    #    - git push --set-upstream origin "$CI_COMMIT_REF_NAME"
    #    - node ./script/deployStoreHomeComponents.js
    - npm run release-ci
    - NODE_ENV=dev node ./script/dingdingNotify.js
  tags:
    - shell
  only:
    - /^(feature|release).*$/

deploy-release:
  stage: deploy
  script:
    - COMMIT_MESSAGE=`echo $(git log -1 --pretty=%B)`
    - echo $CI_COMMIT_AUTHOR
    - echo $CI_COMMIT_TITLE
    - echo $CI_COMMIT_MESSAGE
    - echo $CI_COMMIT_DESCRIPTION
    - echo $CI_JOB_URL
    - echo $CI_MERGE_REQUEST_DIFF_ID
    - echo $CI_COMMIT_BRANCH
    - git checkout -B "release/build" "$CI_COMMIT_SHA"
    - npx lerna version "$CI_COMMIT_TAG" --force-publish -y --no-git-tag-version --conventional-commits
    - git add .
    - git commit -m "bump new release version"
    #    - node ./script/deployStoreHomeComponents.js
    - npm run release-ci
    - node ./script/dingdingNotify.js
  tags:
    - shell
  only:
    - tags

deploy-pipeline:
  stage: deploy
  script:
    - COMMIT_MESSAGE=`echo $(git log -1 --pretty=%B)`
    - echo $CI_COMMIT_AUTHOR
    - echo $CI_COMMIT_TITLE
    - echo $CI_COMMIT_MESSAGE
    - echo $CI_COMMIT_DESCRIPTION
    - echo $CI_JOB_URL
    - echo $CI_MERGE_REQUEST_DIFF_ID
    - echo $CI_COMMIT_BRANCH
    - git checkout -B "release/build" "$CI_COMMIT_SHA"
    - npx lerna version prerelease --force-publish -y --no-git-tag-version --conventional-commits
    - git add .
    - git commit -m "bump new beta version by trigger"
    #    - node ./script/deployStoreHomeComponents.js
    - npm run release-ci
    - node ./script/dingdingNotify.js
  tags:
    - shell
  only:
    - triggers
