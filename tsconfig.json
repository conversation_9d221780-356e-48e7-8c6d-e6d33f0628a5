{
  "compilerOptions": {
    "removeComments": true,
    "incremental": true,
    "allowJs": true,
    "module": "es6",
    "esModuleInterop": true,
    "target": "es6",
    "declaration": true,
    "noImplicitAny": false,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "lib": ["ESNext", "DOM"],
    "baseUrl": "./",
    "paths": {
      "@utils/helper": ["packages/components/lib/utils/helper.js"],
      "@pages/*": ["packages/components/lib/components/*"],
      "@utils": ["packages/components/lib/utils/index"],
      "@utils/*": ["packages/components/lib/utils/*"],
      "@events": ["packages/components/lib/events/index"],
      "@plugins/*": ["packages/components/lib/common/plugins/*"],
      "@plugins": ["packages/components/lib/common/plugins/index"],
      "@wxs/*": ["packages/components/lib/wxs/*"],
      "@npm-components/*": ["./packages/components/lib/components/*"],
      "@wosai/emenu-mini-di": ["./packages/di/lib"],
      "@wosai/emenu-mini-utils": ["./packages/utils/lib"],
      "@wosai/emenu-mini-domaincommon": ["./packages/domain-common/lib"],
      "@sqb/smart-mp-stores": ["./packages/components/lib/stores/index"],
    },
    "types": ["miniprogram-api-typings", "jest"]
  }
}
