// 该配置主要使ide识别别名
const Path = require('path');

function resolve(dir) {
  return Path.join(__dirname, '.', dir);
}
module.exports = {
  context: Path.resolve(__dirname, './'),
  resolve: {
    extensions: ['.js'],
    alias: {
      '@pages': resolve('./packages/components/lib/components'),
      '@utils': resolve('./packages/components/lib/utils'),
      '@events': resolve('./packages/components/lib/events'),
      '@plugins': resolve('./packages/components/lib/common/plugins'),
      '@wxs': resolve('./packages/components/lib/wxs'),
      '@npm-components': resolve('./packages/components/lib/components'),
    },
  },
};
