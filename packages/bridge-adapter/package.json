{"name": "@wosai/emenu-mini-bridge-adapter", "version": "6.7.10-alpha.0", "description": "mini core bridge adapter", "maintainers": ["<PERSON><PERSON><PERSON><PERSON>"], "keywords": ["emenu", "core", "bridge adapter"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://git.wosai-inc.com:MK/emenu-mini-core", "license": "ISC", "main": "dist/index.js", "types": "dist/index.d.ts", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib", "dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "*********************:MK/emenu-mini-core.git"}, "scripts": {"test": "jest", "build": "rollup -c ../../rollup.config.js", "release": "npm publish"}, "dependencies": {"@wosai/emenu-mini-lodash": "6.1.1", "@wosai/emenu-mini-native": "6.1.1", "@wosai/emenu-mini-utils": "6.63.1-alpha.0"}, "gitHead": "f24db26f676901bb8625ccf77b945f1d31f698d1", "devDependencies": {}}