# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [6.7.10-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.0.0...v6.7.10-alpha.0) (2022-08-30)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [6.2.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v6.1.11...v6.2.4) (2022-04-28)

### Bug Fixes

- 修复依赖版本不正确的问题 ([fd2f3c3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/fd2f3c3a9ad1e7ac9f56bb4d648f0d0fdad38170))

## [6.1.11](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v6.1.1...v6.1.11) (2022-04-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [6.1.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v6.1.1) (2022-03-29)

### Bug Fixes

- 恢复被 revert 的代码 ([e9572fb](https://git.wosai-inc.com/MK/emenu-mini-core/commits/e9572fb4dfe4d83b4e7d3ba1a54dc82711bcd722))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 将 emenu 放入 npm 包 ([b9fd193](https://git.wosai-inc.com/MK/emenu-mini-core/commits/b9fd193f52bd8052773f9c8fee29122b0bcc744e))
- 支持收钱吧小程序扫码点单组件化(删除领券组件） ([abfcd23](https://git.wosai-inc.com/MK/emenu-mini-core/commits/abfcd23898b246f77d606ee9270c9dab97d89d6f))
- 支持收钱吧小程序独立分包 ([39752d7](https://git.wosai-inc.com/MK/emenu-mini-core/commits/39752d73e752e07aa164a42ff8d372584b2f0626))
- 更新喜茶主题（WIP) ([f309a5f](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f309a5f0f73f3a5aa7e47e02730a269fd7268fe4))
- 更新营销组件版本，支持支付宝大包 ([ff825d8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ff825d8edb87cfbdd5420cef8a96737805e2f2ea))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [6.0.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v6.0.4) (2022-01-14)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 将 emenu 放入 npm 包 ([b9fd193](https://git.wosai-inc.com/MK/emenu-mini-core/commits/b9fd193f52bd8052773f9c8fee29122b0bcc744e))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.9](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.9) (2021-09-30)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.8](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.8) (2021-09-30)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.7](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.7) (2021-09-29)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.6](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.6) (2021-09-29)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.5](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.5) (2021-09-29)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.4) (2021-09-28)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.3](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.3) (2021-09-28)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.1) (2021-09-28)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.495 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.494 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.493 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.492 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.491 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.490 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.489 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.488 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.487 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.486 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.485 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.484 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.483 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.482 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.481 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.480 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.479 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.478 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.477 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.476 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.475 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.475-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.474-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.473-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.472-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.471-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.470-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.469-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.468-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.467-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.466-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.465-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.464-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.463-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.462-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.461-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.460-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.459-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.458-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.457-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.456-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.455-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.454-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.453-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.452-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.451-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.450-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.449-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.448-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.447-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.446-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.445-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.444-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.443-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.442-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.441-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.440-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.439-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.438-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.437-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.436-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.435-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.434-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.433-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.432-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.431-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.430-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.429-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.428-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.427-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.426-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.425-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.424-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.423-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.422-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.421-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.420-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.419-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.418-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.417-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.416-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.415-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.414-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.413-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.412-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.411-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.410-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.409-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.408-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.407-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.406-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.405-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.404-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.403-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.402-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.401-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.400-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.399-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.398-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.397-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.396-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.395-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.394-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.393-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.392-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.391-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.390-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.389-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.388-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.387-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.386-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.385-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.384-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.383-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.382-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.381-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.380-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.379-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.378-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.377-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.376-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.375-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.374-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.373-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.372-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.371-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.370-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.369-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.368-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.367-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.366-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.365-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.364-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.363-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.362-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.361-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.360-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.359-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.358-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.357-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.356-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.355-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.354-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.353-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.352-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.351-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.350-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.349-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.348-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.347-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.346-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.345-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.344-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.343-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.342-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.341-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.340-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.339-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.338-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.337-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.336-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.335-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.334-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.333-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.332-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.331-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.330-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.329-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.328-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.327-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.326-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.325-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.324-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.323-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.322-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.321-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.320-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.319-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.318-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.317-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.316-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.315-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.314-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.313-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.312-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.311-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.310-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.309-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.308-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.307-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.306-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.305-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.304-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.303-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.302-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.301-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.300-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.299-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.298-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.297-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.296-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.295-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.294-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.293-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.292-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.291-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.290-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.289-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.288-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.287-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.286-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.285-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.284-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.283-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.282-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.281-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.280-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.279-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.278-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.277-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.276-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.275-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.274-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.273-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.272-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.271-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.270-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.269-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.268-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.267-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.266-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.265-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.264-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.263-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.262-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.261-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.260-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.259-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.258-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.257-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.256-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.255-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.254-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.253-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.252-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.251-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.250-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.249-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.248-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.247-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.246-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.245-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.244-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.243-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.242-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.241 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.241-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.240-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.239-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.238-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.237-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.236-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.235-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.234-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.233-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.232-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.231-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.230-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.229-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.228-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.227-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.226-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.225-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.224-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.223-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.222-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.221-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.220-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.219-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.218-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.217-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.216-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.215-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.214-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.213-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.212-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.211-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.210-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.209-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.208-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.207-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.206-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.205-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.204-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.203-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.202-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.201-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.200-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.199-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.198-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.197-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.196-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.195-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.194-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.193-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.192-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.191-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.190-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.189-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.188-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.187-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.186-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.185-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.184-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.183-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.182-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.181-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.180-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.179-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.178-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.177-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.176-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.175-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.174-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.173-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.172-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.171-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.170-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.169-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.168-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.167-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.166-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.165-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.164-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.163-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.162-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.161-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.160-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.159-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.158-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.157-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.156-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.155-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.154-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.153-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.152-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.151-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.150-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.149-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.148-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.147-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.146-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.145-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.144-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.143-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.142-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.141-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.140-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.139-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.138-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.137-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.136-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.135-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.134-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.133-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.132-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.131-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.130-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.129-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.128-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.127-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.126-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.125-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.124-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.123-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.122-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.121-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.120-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.119-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.118-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.117-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.116-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.115-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.114-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.113-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.112-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.111-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.110-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.109-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.108-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.107-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.106-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.105-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.104-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.103-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.102-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.101-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.100-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.99-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.98-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.97-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.96-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.95-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.94-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.93-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.92-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.91-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.90-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.89-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.88-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.87-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.86-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.85-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.84-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.83-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.82-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.81-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.80-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.79-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.78-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.77-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.76-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.75-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.74-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.73-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.72-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.71-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.70-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.69-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.68-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.67-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.66-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.65-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.64-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.63-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.62-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.61-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.60-alpha.0 (2021-09-27)

### Bug Fixes

- 点单插件-购物车】已经加购了必选品，还是提示“请点必选品” ([ac8b9f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ac8b9f3de3ba14ff3d3666ad79c5d839a746dd79))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.59-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.58-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.57-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.56-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.55-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.54-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.53-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.52-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.51-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.50-alpha.0 (2021-09-27)

### Features

- 增加 PAGES 常量 ([2c542af](https://git.wosai-inc.com/MK/emenu-mini-core/commits/2c542aff12cde035635a16b5069f80a4de3d2874))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.49-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.48-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.47-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.46-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.45-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.44-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.43-alpha.0 (2021-09-27)

### Bug Fixes

- 【C-聚合页】店铺堂食，外卖均开启，进入门店点单页应有弹层，且默认选中堂食（前端传参缺少 storeSetId）#SMART-4956 ([63f2847](https://git.wosai-inc.com/MK/emenu-mini-core/commits/63f2847eaad9af62539039c2f3e1fdc72f077763)), closes [#SMART-4956](https://git.wosai-inc.com/MK/emenu-mini-core/issues/SMART-4956)

## 4.5.42-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.41-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 4.5.40-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.39-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))
- 调整使用统一日志 ([d23b964](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d23b9648bf5262749b14a32f167a28b9e9444732))

## 4.5.38-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.37-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.36-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.35-alpha.0 (2021-09-27)

### Features

- BIG UPDATE, HOOKS 有 BUGS， 使用 Promise 替换 HOOKS 处理数据流 ([4bd912c](https://git.wosai-inc.com/MK/emenu-mini-core/commits/4bd912ce46117768c86354cc4d8c11ce8118265c))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.34-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))
- 购物车为空时，不需要拉优惠信息 ([6d49ea2](https://git.wosai-inc.com/MK/emenu-mini-core/commits/6d49ea233351ab061bbde0a15d345f9599f42421))

## 4.5.33-alpha.0 (2021-09-27)

### Features

- 删除 discountPlugin, 并重写拉取优惠，支持不重复拉取优惠信息 ([dcbce2e](https://git.wosai-inc.com/MK/emenu-mini-core/commits/dcbce2e7e2818d14131dc3cc28d3fc3ead560109))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.32-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.31-alpha.0 (2021-09-27)

### Bug Fixes

- 点单插件-购物车】已经加购了必选品，还是提示“请点必选品” ([ac8b9f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ac8b9f3de3ba14ff3d3666ad79c5d839a746dd79))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.30-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.29-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.28-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.27-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.26-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.25-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.24-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.23-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.22-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.21-alpha.0 (2021-09-27)

### Features

- 增加 PAGES 常量 ([2c542af](https://git.wosai-inc.com/MK/emenu-mini-core/commits/2c542aff12cde035635a16b5069f80a4de3d2874))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.20-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.19-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.17-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.5.16](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.16) (2021-09-27)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.5.15](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.15) (2021-09-25)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.5.14](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.14) (2021-09-24)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.5.13](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.13) (2021-09-23)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [4.5.11](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.11) (2021-09-23)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [4.5.8](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.8) (2021-09-23)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [4.5.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.4) (2021-09-22)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [4.5.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.1) (2021-09-22)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [4.4.2](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.4.2) (2021-09-22)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [4.3.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.3.1) (2021-09-22)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [4.2.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.2.1) (2021-09-19)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [4.1.3](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.1.3) (2021-09-18)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [4.0.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.0.1) (2021-09-14)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.10](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.29...@wosai/emenu-mini-bridge-adapter@3.2.10) (2021-09-09)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.9](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.29...@wosai/emenu-mini-bridge-adapter@3.2.9) (2021-09-09)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.8](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.29...@wosai/emenu-mini-bridge-adapter@3.2.8) (2021-09-02)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.7](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.29...@wosai/emenu-mini-bridge-adapter@3.2.7) (2021-09-02)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.6](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.29...@wosai/emenu-mini-bridge-adapter@3.2.6) (2021-09-02)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.5](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.29...@wosai/emenu-mini-bridge-adapter@3.2.5) (2021-09-02)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v3.2.4) (2021-09-01)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v3.2.1) (2021-08-27)

### Features

- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.1.2](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v3.1.2) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.29](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.28...@wosai/emenu-mini-bridge-adapter@3.0.29) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.28](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.27...@wosai/emenu-mini-bridge-adapter@3.0.28) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.27](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.26...@wosai/emenu-mini-bridge-adapter@3.0.27) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.26](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.25-alpha.0...@wosai/emenu-mini-bridge-adapter@3.0.26) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.25](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.25-alpha.0...@wosai/emenu-mini-bridge-adapter@3.0.25) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.25-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.24-alpha.0...@wosai/emenu-mini-bridge-adapter@3.0.25-alpha.0) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.24-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.23...@wosai/emenu-mini-bridge-adapter@3.0.24-alpha.0) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.23](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.22...@wosai/emenu-mini-bridge-adapter@3.0.23) (2021-08-25)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.22](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.21...@wosai/emenu-mini-bridge-adapter@3.0.22) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.21](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.20...@wosai/emenu-mini-bridge-adapter@3.0.21) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.20](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.19...@wosai/emenu-mini-bridge-adapter@3.0.20) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.19](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.18...@wosai/emenu-mini-bridge-adapter@3.0.19) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.18](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.17...@wosai/emenu-mini-bridge-adapter@3.0.18) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.17](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.16...@wosai/emenu-mini-bridge-adapter@3.0.17) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.16](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.15...@wosai/emenu-mini-bridge-adapter@3.0.16) (2021-08-09)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.15](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.14...@wosai/emenu-mini-bridge-adapter@3.0.15) (2021-08-09)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.14](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.13...@wosai/emenu-mini-bridge-adapter@3.0.14) (2021-08-09)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.13](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.12...@wosai/emenu-mini-bridge-adapter@3.0.13) (2021-08-06)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.12](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.11...@wosai/emenu-mini-bridge-adapter@3.0.12) (2021-08-06)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.11](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.10...@wosai/emenu-mini-bridge-adapter@3.0.11) (2021-08-06)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.10](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.9...@wosai/emenu-mini-bridge-adapter@3.0.10) (2021-08-06)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.9](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.8...@wosai/emenu-mini-bridge-adapter@3.0.9) (2021-08-06)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.8](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.7...@wosai/emenu-mini-bridge-adapter@3.0.8) (2021-08-06)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.7](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.5...@wosai/emenu-mini-bridge-adapter@3.0.7) (2021-08-06)

## 3.0.6 (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.6](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.1...v3.0.6) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.5](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.4...@wosai/emenu-mini-bridge-adapter@3.0.5) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.3...@wosai/emenu-mini-bridge-adapter@3.0.4) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.3](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@3.0.2...@wosai/emenu-mini-bridge-adapter@3.0.3) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.2](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.140...@wosai/emenu-mini-bridge-adapter@3.0.2) (2021-08-05)

## 3.0.1 (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [3.0.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.4.8...v3.0.1) (2021-08-05)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复 this 不正确的问题 ([ef9b253](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ef9b25345a4a8a7ee9a4a922c439322a6dda346e))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

### Features

- export sqbComponent ([10337f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/10337f39cb933cb2afb1b682f039243cd1501315))
- request 使用 get 请求不使用 debug 参数 ([e14f571](https://git.wosai-inc.com/MK/emenu-mini-core/commits/e14f571127ab2d477afbd1de457bac0cdf6efdfa))
- 临时修复 adapter ([1595b44](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1595b44461284b171bc92849331bb38fdb87e7d8))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))
- 临时修复 adapter ([5e2b77a](https://git.wosai-inc.com/MK/emenu-mini-core/commits/5e2b77a475586c11c10e5ec0160dfd541faaff34))
- 临时修复 adapter ([861e0c1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/861e0c1654a3e01ce664caf92a36cfd557945324))
- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))
- 更新 ROUTE_MAP ([91c1708](https://git.wosai-inc.com/MK/emenu-mini-core/commits/91c17086df3afb07f07f8a10526fd406a877a470))
- **bridge:** navigateTo 增加 navigateBack 配置 ([848bba1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/848bba15feb8716888f842819d2f246b17e97c85))
- **bridge:** navigateTo 增加 navigateBack 配置 ([fec67c8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/fec67c8f5143de2dd922e95e4b7fe9aee4d12902))
- 增加路由映射 ([6eb3487](https://git.wosai-inc.com/MK/emenu-mini-core/commits/6eb3487e568cdf4392355dd55dff440dea6bdba2))

## [1.4.140](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.139...@wosai/emenu-mini-bridge-adapter@1.4.140) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.139](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.138...@wosai/emenu-mini-bridge-adapter@1.4.139) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.138](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.137...@wosai/emenu-mini-bridge-adapter@1.4.138) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.137](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.136...@wosai/emenu-mini-bridge-adapter@1.4.137) (2021-08-05)

### Features

- 更新 ROUTE_MAP ([91c1708](https://git.wosai-inc.com/MK/emenu-mini-core/commits/91c17086df3afb07f07f8a10526fd406a877a470))

## [1.4.136](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.135...@wosai/emenu-mini-bridge-adapter@1.4.136) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.135](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.134...@wosai/emenu-mini-bridge-adapter@1.4.135) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.134](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.133...@wosai/emenu-mini-bridge-adapter@1.4.134) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.133](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.132...@wosai/emenu-mini-bridge-adapter@1.4.133) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.132](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.131...@wosai/emenu-mini-bridge-adapter@1.4.132) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.131](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.130...@wosai/emenu-mini-bridge-adapter@1.4.131) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.130](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.128...@wosai/emenu-mini-bridge-adapter@1.4.130) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.129](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.128...@wosai/emenu-mini-bridge-adapter@1.4.129) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.128](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.127...@wosai/emenu-mini-bridge-adapter@1.4.128) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.127](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.126...@wosai/emenu-mini-bridge-adapter@1.4.127) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.126](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.125...@wosai/emenu-mini-bridge-adapter@1.4.126) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.125](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.124...@wosai/emenu-mini-bridge-adapter@1.4.125) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.124](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.93...@wosai/emenu-mini-bridge-adapter@1.4.124) (2021-08-04)

## 2.0.2 (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.123](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.123-alpha.0...@wosai/emenu-mini-bridge-adapter@1.4.123) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.123-alpha.2](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.93...@wosai/emenu-mini-bridge-adapter@1.4.123-alpha.2) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.123-alpha.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.93...@wosai/emenu-mini-bridge-adapter@1.4.123-alpha.1) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.123-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.122...@wosai/emenu-mini-bridge-adapter@1.4.123-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.122](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.122-alpha.0...@wosai/emenu-mini-bridge-adapter@1.4.122) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.122-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.121-alpha.0...@wosai/emenu-mini-bridge-adapter@1.4.122-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.121-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.120-alpha.0...@wosai/emenu-mini-bridge-adapter@1.4.121-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.120-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.119-alpha.0...@wosai/emenu-mini-bridge-adapter@1.4.120-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.119-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.118-alpha.0...@wosai/emenu-mini-bridge-adapter@1.4.119-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.118-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.117-alpha.0...@wosai/emenu-mini-bridge-adapter@1.4.118-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.117-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.116...@wosai/emenu-mini-bridge-adapter@1.4.117-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.116](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.115...@wosai/emenu-mini-bridge-adapter@1.4.116) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.115](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.114...@wosai/emenu-mini-bridge-adapter@1.4.115) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.114](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.113...@wosai/emenu-mini-bridge-adapter@1.4.114) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.113](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.112...@wosai/emenu-mini-bridge-adapter@1.4.113) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.112](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.111...@wosai/emenu-mini-bridge-adapter@1.4.112) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.111](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.110...@wosai/emenu-mini-bridge-adapter@1.4.111) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.110](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.109...@wosai/emenu-mini-bridge-adapter@1.4.110) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.109](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.101...@wosai/emenu-mini-bridge-adapter@1.4.109) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.108](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.101...@wosai/emenu-mini-bridge-adapter@1.4.108) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.107](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.101...@wosai/emenu-mini-bridge-adapter@1.4.107) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.106](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.101...@wosai/emenu-mini-bridge-adapter@1.4.106) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.105](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.101...@wosai/emenu-mini-bridge-adapter@1.4.105) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.104](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.101...@wosai/emenu-mini-bridge-adapter@1.4.104) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.103](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.101...@wosai/emenu-mini-bridge-adapter@1.4.103) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.102](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.101...@wosai/emenu-mini-bridge-adapter@1.4.102) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.101](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.93...@wosai/emenu-mini-bridge-adapter@1.4.101) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.100](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.93...@wosai/emenu-mini-bridge-adapter@1.4.100) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.99](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.93...@wosai/emenu-mini-bridge-adapter@1.4.99) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.98](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.93...@wosai/emenu-mini-bridge-adapter@1.4.98) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.97](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.93...@wosai/emenu-mini-bridge-adapter@1.4.97) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.96](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.93...@wosai/emenu-mini-bridge-adapter@1.4.96) (2021-08-01)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.95](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.93...@wosai/emenu-mini-bridge-adapter@1.4.95) (2021-08-01)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.94](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.93...@wosai/emenu-mini-bridge-adapter@1.4.94) (2021-08-01)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.93](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.91...@wosai/emenu-mini-bridge-adapter@1.4.93) (2021-08-01)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.92](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.91...@wosai/emenu-mini-bridge-adapter@1.4.92) (2021-08-01)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.91](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.91) (2021-08-01)

### Bug Fixes

- 修复 this 不正确的问题 ([ef9b253](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ef9b25345a4a8a7ee9a4a922c439322a6dda346e))

### Features

- export sqbComponent ([10337f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/10337f39cb933cb2afb1b682f039243cd1501315))
- request 使用 get 请求不使用 debug 参数 ([e14f571](https://git.wosai-inc.com/MK/emenu-mini-core/commits/e14f571127ab2d477afbd1de457bac0cdf6efdfa))
- 临时修复 adapter ([1595b44](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1595b44461284b171bc92849331bb38fdb87e7d8))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))
- 临时修复 adapter ([5e2b77a](https://git.wosai-inc.com/MK/emenu-mini-core/commits/5e2b77a475586c11c10e5ec0160dfd541faaff34))
- 临时修复 adapter ([861e0c1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/861e0c1654a3e01ce664caf92a36cfd557945324))
- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))
- **bridge:** navigateTo 增加 navigateBack 配置 ([848bba1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/848bba15feb8716888f842819d2f246b17e97c85))
- **bridge:** navigateTo 增加 navigateBack 配置 ([fec67c8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/fec67c8f5143de2dd922e95e4b7fe9aee4d12902))

## [1.4.90](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.90) (2021-08-01)

### Features

- export sqbComponent ([10337f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/10337f39cb933cb2afb1b682f039243cd1501315))
- request 使用 get 请求不使用 debug 参数 ([e14f571](https://git.wosai-inc.com/MK/emenu-mini-core/commits/e14f571127ab2d477afbd1de457bac0cdf6efdfa))
- 临时修复 adapter ([1595b44](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1595b44461284b171bc92849331bb38fdb87e7d8))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))
- 临时修复 adapter ([5e2b77a](https://git.wosai-inc.com/MK/emenu-mini-core/commits/5e2b77a475586c11c10e5ec0160dfd541faaff34))
- 临时修复 adapter ([861e0c1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/861e0c1654a3e01ce664caf92a36cfd557945324))
- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))
- **bridge:** navigateTo 增加 navigateBack 配置 ([848bba1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/848bba15feb8716888f842819d2f246b17e97c85))
- **bridge:** navigateTo 增加 navigateBack 配置 ([fec67c8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/fec67c8f5143de2dd922e95e4b7fe9aee4d12902))

## [1.4.89](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.89) (2021-07-30)

### Features

- export sqbComponent ([10337f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/10337f39cb933cb2afb1b682f039243cd1501315))
- request 使用 get 请求不使用 debug 参数 ([e14f571](https://git.wosai-inc.com/MK/emenu-mini-core/commits/e14f571127ab2d477afbd1de457bac0cdf6efdfa))
- 临时修复 adapter ([1595b44](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1595b44461284b171bc92849331bb38fdb87e7d8))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))
- 临时修复 adapter ([5e2b77a](https://git.wosai-inc.com/MK/emenu-mini-core/commits/5e2b77a475586c11c10e5ec0160dfd541faaff34))
- 临时修复 adapter ([861e0c1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/861e0c1654a3e01ce664caf92a36cfd557945324))
- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))
- **bridge:** navigateTo 增加 navigateBack 配置 ([848bba1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/848bba15feb8716888f842819d2f246b17e97c85))
- **bridge:** navigateTo 增加 navigateBack 配置 ([fec67c8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/fec67c8f5143de2dd922e95e4b7fe9aee4d12902))

## [1.4.88](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.88) (2021-07-29)

### Features

- export sqbComponent ([10337f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/10337f39cb933cb2afb1b682f039243cd1501315))
- request 使用 get 请求不使用 debug 参数 ([e14f571](https://git.wosai-inc.com/MK/emenu-mini-core/commits/e14f571127ab2d477afbd1de457bac0cdf6efdfa))
- 临时修复 adapter ([1595b44](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1595b44461284b171bc92849331bb38fdb87e7d8))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))
- 临时修复 adapter ([5e2b77a](https://git.wosai-inc.com/MK/emenu-mini-core/commits/5e2b77a475586c11c10e5ec0160dfd541faaff34))
- 临时修复 adapter ([861e0c1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/861e0c1654a3e01ce664caf92a36cfd557945324))
- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))
- **bridge:** navigateTo 增加 navigateBack 配置 ([848bba1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/848bba15feb8716888f842819d2f246b17e97c85))
- **bridge:** navigateTo 增加 navigateBack 配置 ([fec67c8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/fec67c8f5143de2dd922e95e4b7fe9aee4d12902))

## [1.4.87](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.87) (2021-07-29)

### Features

- request 使用 get 请求不使用 debug 参数 ([e14f571](https://git.wosai-inc.com/MK/emenu-mini-core/commits/e14f571127ab2d477afbd1de457bac0cdf6efdfa))
- 临时修复 adapter ([1595b44](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1595b44461284b171bc92849331bb38fdb87e7d8))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))
- 临时修复 adapter ([5e2b77a](https://git.wosai-inc.com/MK/emenu-mini-core/commits/5e2b77a475586c11c10e5ec0160dfd541faaff34))
- 临时修复 adapter ([861e0c1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/861e0c1654a3e01ce664caf92a36cfd557945324))
- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))
- **bridge:** navigateTo 增加 navigateBack 配置 ([848bba1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/848bba15feb8716888f842819d2f246b17e97c85))
- **bridge:** navigateTo 增加 navigateBack 配置 ([fec67c8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/fec67c8f5143de2dd922e95e4b7fe9aee4d12902))

## [1.4.86](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.86) (2021-07-29)

### Features

- request 使用 get 请求不使用 debug 参数 ([e14f571](https://git.wosai-inc.com/MK/emenu-mini-core/commits/e14f571127ab2d477afbd1de457bac0cdf6efdfa))
- 临时修复 adapter ([1595b44](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1595b44461284b171bc92849331bb38fdb87e7d8))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))
- 临时修复 adapter ([5e2b77a](https://git.wosai-inc.com/MK/emenu-mini-core/commits/5e2b77a475586c11c10e5ec0160dfd541faaff34))
- 临时修复 adapter ([861e0c1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/861e0c1654a3e01ce664caf92a36cfd557945324))
- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))

## [1.4.85](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.85) (2021-07-29)

### Features

- 临时修复 adapter ([1595b44](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1595b44461284b171bc92849331bb38fdb87e7d8))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))
- 临时修复 adapter ([5e2b77a](https://git.wosai-inc.com/MK/emenu-mini-core/commits/5e2b77a475586c11c10e5ec0160dfd541faaff34))
- 临时修复 adapter ([861e0c1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/861e0c1654a3e01ce664caf92a36cfd557945324))
- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))

## [1.4.84](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.84) (2021-07-29)

### Features

- 临时修复 adapter ([1595b44](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1595b44461284b171bc92849331bb38fdb87e7d8))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))
- 临时修复 adapter ([5e2b77a](https://git.wosai-inc.com/MK/emenu-mini-core/commits/5e2b77a475586c11c10e5ec0160dfd541faaff34))
- 临时修复 adapter ([861e0c1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/861e0c1654a3e01ce664caf92a36cfd557945324))
- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))

## [1.4.83](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.83) (2021-07-29)

### Features

- 临时修复 adapter ([5e2b77a](https://git.wosai-inc.com/MK/emenu-mini-core/commits/5e2b77a475586c11c10e5ec0160dfd541faaff34))
- 临时修复 adapter ([861e0c1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/861e0c1654a3e01ce664caf92a36cfd557945324))
- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))

## [1.4.82](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.82) (2021-07-29)

### Features

- 临时修复 adapter ([861e0c1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/861e0c1654a3e01ce664caf92a36cfd557945324))
- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))

## [1.4.81](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.81) (2021-07-29)

### Features

- 临时修复 adapter ([c0cfa28](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c0cfa28c4bab8dd2f8c2c3b11361ee1c527bc8cc))

## [1.4.80](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.80) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.79](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.79) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.78](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.78) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.77](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.77) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.76](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.76) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.75](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.75) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.74](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.74) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.73](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.73) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.72](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.72) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.71](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.71) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.70](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.70) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.69](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.68...@wosai/emenu-mini-bridge-adapter@1.4.69) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.68](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.66...@wosai/emenu-mini-bridge-adapter@1.4.68) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.67](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.66...@wosai/emenu-mini-bridge-adapter@1.4.67) (2021-07-28)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.66](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.64...@wosai/emenu-mini-bridge-adapter@1.4.66) (2021-07-28)

### Features

- 增加路由映射 ([6eb3487](https://git.wosai-inc.com/MK/emenu-mini-core/commits/6eb3487e568cdf4392355dd55dff440dea6bdba2))

## [1.4.65](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.64...@wosai/emenu-mini-bridge-adapter@1.4.65) (2021-07-28)

### Features

- 增加路由映射 ([6eb3487](https://git.wosai-inc.com/MK/emenu-mini-core/commits/6eb3487e568cdf4392355dd55dff440dea6bdba2))

## [1.4.64](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.59...@wosai/emenu-mini-bridge-adapter@1.4.64) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.63](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.59...@wosai/emenu-mini-bridge-adapter@1.4.63) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.62](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.59...@wosai/emenu-mini-bridge-adapter@1.4.62) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.61](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.59...@wosai/emenu-mini-bridge-adapter@1.4.61) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.60](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.59...@wosai/emenu-mini-bridge-adapter@1.4.60) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.59](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.59) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.4.58](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.58) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.4.57](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.57) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.4.56](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.56) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.4.55](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.55) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.4.54](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.54) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.4.53](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.53) (2021-07-27)

### Bug Fixes

- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.4.52](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.52) (2021-07-27)

### Bug Fixes

- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.4.51](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.51) (2021-07-27)

### Bug Fixes

- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.4.50](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.50) (2021-07-27)

### Bug Fixes

- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.4.49](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.49) (2021-07-27)

### Bug Fixes

- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.4.48](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.48) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.47](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.47) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.46](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.46) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.45](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.45) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.44](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.43...@wosai/emenu-mini-bridge-adapter@1.4.44) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.43](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.43) (2021-07-26)

## 1.4.33 (2021-07-23)

## 1.4.32 (2021-07-23)

## 1.4.31 (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.42](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.42) (2021-07-26)

## 1.4.33 (2021-07-23)

## 1.4.32 (2021-07-23)

## 1.4.31 (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.41](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.41) (2021-07-26)

## 1.4.33 (2021-07-23)

## 1.4.32 (2021-07-23)

## 1.4.31 (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.40](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.40) (2021-07-26)

## 1.4.33 (2021-07-23)

## 1.4.32 (2021-07-23)

## 1.4.31 (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.39](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.39) (2021-07-23)

## 1.4.33 (2021-07-23)

## 1.4.32 (2021-07-23)

## 1.4.31 (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.38](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.38) (2021-07-23)

## 1.4.33 (2021-07-23)

## 1.4.32 (2021-07-23)

## 1.4.31 (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.38-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.38-alpha.0) (2021-07-23)

## 1.4.33 (2021-07-23)

## 1.4.32 (2021-07-23)

## 1.4.31 (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.37-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.37-alpha.0) (2021-07-23)

## 1.4.33 (2021-07-23)

## 1.4.32 (2021-07-23)

## 1.4.31 (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.36-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.36-alpha.0) (2021-07-23)

## 1.4.33 (2021-07-23)

## 1.4.32 (2021-07-23)

## 1.4.31 (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.35-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.35-alpha.0) (2021-07-23)

## 1.4.33 (2021-07-23)

## 1.4.32 (2021-07-23)

## 1.4.31 (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.34-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.34-alpha.0) (2021-07-23)

## 1.4.33 (2021-07-23)

## 1.4.32 (2021-07-23)

## 1.4.31 (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.33](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-bridge-adapter@1.4.22...@wosai/emenu-mini-bridge-adapter@1.4.33) (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.32](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.4.23...v1.4.32) (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.31](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.4.23...v1.4.31) (2021-07-23)

## 1.4.29 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## 1.4.22 (2021-07-23)

## 1.4.21 (2021-07-23)

## 1.4.6 (2021-07-23)

### Features

- 删除无用文件 ([476b3b5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/476b3b5eaffceab6d89324bba0f2daa21cce47a5))
- 升级 bridge 版本 ([64d5c9d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/64d5c9dee2844b46e7bb45af235c35e9c9019227))
- 增加 models ([b05042c](https://git.wosai-inc.com/MK/emenu-mini-core/commits/b05042cdc5112ae4bab2b80df2d7cae530cf36b0))
- 增加库 bridge-adapter ([b1a84ac](https://git.wosai-inc.com/MK/emenu-mini-core/commits/b1a84acd64f07bfea71453f4fc6cc45dd3e17527))
- 更改 rollup 配置 ([94166f8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/94166f86190650b353bd17cd940207426b9f6ac2))
- 更改 rollup 配置 ([9b8763f](https://git.wosai-inc.com/MK/emenu-mini-core/commits/9b8763f87d45c854426cf442f7b5eff69371c772))

## [1.4.21](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.4.6...v1.4.21) (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-bridge-adapter

## [1.4.6](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.4.7...v1.4.6) (2021-07-23)

### Features

- 删除无用文件 ([476b3b5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/476b3b5eaffceab6d89324bba0f2daa21cce47a5))
- 升级 bridge 版本 ([64d5c9d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/64d5c9dee2844b46e7bb45af235c35e9c9019227))
- 增加 models ([b05042c](https://git.wosai-inc.com/MK/emenu-mini-core/commits/b05042cdc5112ae4bab2b80df2d7cae530cf36b0))
- 增加库 bridge-adapter ([b1a84ac](https://git.wosai-inc.com/MK/emenu-mini-core/commits/b1a84acd64f07bfea71453f4fc6cc45dd3e17527))
- 更改 rollup 配置 ([94166f8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/94166f86190650b353bd17cd940207426b9f6ac2))
- 更改 rollup 配置 ([9b8763f](https://git.wosai-inc.com/MK/emenu-mini-core/commits/9b8763f87d45c854426cf442f7b5eff69371c772))

## [1.4.2](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.4.1...v1.4.2) (2021-07-15)

**Note:** Version bump only for package @wosai/emenu-mini-md5
