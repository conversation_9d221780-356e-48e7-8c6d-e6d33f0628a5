import _ from '@wosai/emenu-mini-lodash';
import sqb from '@wosai/emenu-mini-native';
// import Request from '../utils/request';
import { isWeixin, ObjectUtils, StorageUtils, StringUtils } from '@wosai/emenu-mini-utils';
import { CONFIGS, MP_ROOT_PATH, PLUGIN_HOME_PATH, routeMap, SYNC_UNION_EVENT } from './const';
// import { setContext, getContext } from '@wosai/emenu-mini-component';
import { attr, bridge } from './decorator';
// import { Conf } from '../utils/apollo';

const { storage } = StorageUtils;
const { guid, mergeQuery } = StringUtils;
const { camelCaseKeys } = ObjectUtils;
// const request = Request.request;
const isWeapp = isWeixin();
const traceId = guid();
let Conf;
let Request;

const __slsqueue__ = [];
let __slsgroup__ = [];

export function setConf(conf) {
  Conf = conf;
}

export function setRequest(request) {
  Request = request;
}

/**
 * 定时发送日志至sls
 * @param func
 * @param timeout
 */
function pollSLSLogs(func, timeout = 3000) {
  const SLS_SEND_LOGS_TIMEOUT = _.get(Conf, 'SLS_SEND_LOGS_TIMEOUT', timeout);
  const toGroupLogs = () => {
    const slsqueue = __slsqueue__.splice(0);
    const groupLogs = _.chain(slsqueue)
      .groupBy((item) => item[1])
      .map((value, key) => {
        const __logs__ = _.map(value, (log) => log[0]);
        return {
          __topic__: key,
          __logs__,
        };
      })
      .value();
    __slsgroup__ = [...__slsgroup__, ...groupLogs];
  };

  setTimeout(() => {
    toGroupLogs();
    const params = __slsgroup__.shift();
    params && func(params);
    pollSLSLogs(func);
  }, SLS_SEND_LOGS_TIMEOUT);
}

pollSLSLogs(async (params) => {
  // const getBodySize = _params => {
  //   let result
  //   try {
  //     result = JSON.stringify(_params).length
  //   } catch (err) {
  //     sentryInfo(err)
  //   }
  //   return result
  // }
  const sendLogs = async ({ __topic__, __logs__ } = {}) => {
    const url = 'https://mk-group.cn-hangzhou.log.aliyuncs.com/logstores/emenu-mini/track';
    // const storeId = _.get(this.getStoreInfo(), "id");

    const header = {
      'x-log-apiversion': '0.6.0',
      'x-log-bodyrawsize': '2048',
    };

    if (__topic__ && __logs__) {
      const body = {
        __topic__,
        __logs__,
        // __tags__: tags,
      };
      sqb.request({
        url,
        method: 'POST',
        data: body,
        header,
        fail: function (err) {
          // sentryInfo(err);
        },
      });
    }
  };

  await sendLogs(params);
});

/**
 * 根据传递的参数转化为 收钱吧小程序 所用的配置项
 *  - target取值
 *    - 包含 `plugin`
 *      - `component` **在** `routeMap` 内： `page`
 *      - `component` **不在** `routeMap` 内： `plugin`
 *    - 包含 `appId` 参数字段： `miniprogram`
 *    - `path` 包含 `/pages/web/index`： `webview`
 *    - 其余 `page`
 * @param {*} opts
 * @returns
 */
export function getMpBridgeUrlOptions(opts = {}) {
  if (_.isString(opts)) opts = { path: opts };

  let options = {
    path: '/page-router',
    appId: '',
    query: {},
    replace: false,
    webProtocal: 'https:',
  };
  let { plugin, path, ...rest } = opts;
  let target = 'page';

  if (_.isPlainObject(plugin) && !_.isNull(plugin)) {
    const { name, component, appId } = plugin;
    if (!appId && routeMap[component]) {
      // 跳转到页面
      path = `/${PLUGIN_HOME_PATH}/${routeMap[component]}/index`;
    } else {
      // 跳转到插件
      target = 'plugin';
      path = `${name}/${component}`;
      _.merge(options, {
        appId,
      });
    }
  } else if (_.startsWith(path, '/pages/web/index')) {
    // 跳转到webview
    target = 'webview';
    path = _.get(rest, 'query.url');
  } else if (_.get(rest, 'appId')) {
    // 跳转到其它小程序
    target = 'miniprogram';
  } else {
    // 跳转到小程序页面
    target = 'page';
  }

  // 包含 __is_back__ 参数表示返回
  const delta = _.get(rest, 'query.__is_back__');
  if (delta) return this.navigateBack({ delta });

  _.merge(options, rest, {
    target,
    path,
  });

  return options;
}

/**
 * 小程序插件所用 bridge
 */
export class BaseBridgeAdapter {
  constructor(bridge, bridge_name = 'Bridge') {
    this.__bridge__ = bridge;
    this.__bridge_name__ = bridge_name;
  }

  @bridge()
  requestPayment() {}

  @bridge()
  onGetPhoneNumber() {}

  @bridge()
  onGetUserInfo() {}

  @bridge()
  getPhoneNumber() {}

  @bridge()
  getUserInfo() {}

  @bridge()
  getLocation() {}

  @bridge()
  chooseAddress() {}

  @bridge()
  setNavigation() {}

  @bridge()
  setShareAppMessage() {}

  @bridge()
  setShareTimeline() {}

  @bridge()
  getTerminalInfo() {}

  @bridge()
  refreshTerminalInfo() {}

  @bridge()
  getStoreInfo() {}

  @bridge()
  getMiniProgramConfig() {}

  @bridge()
  getMiniProgramScene() {}

  @bridge()
  getMiniProgramToken() {}

  @bridge()
  getMiniProgramUser() {}

  @bridge()
  getMiniProgramUrl() {}

  @bridge()
  getMiniProgramQuery() {}

  @bridge()
  scanCode() {}

  @bridge()
  navigateTo() {}

  @bridge()
  redirectTo() {}

  @bridge()
  navigateBack() {}

  @bridge()
  requestSubscribeMessage() {}

  @bridge()
  getMiniProgramProfile() {}

  @bridge()
  getMiniProgramEnv() {}

  @bridge()
  track() {}

  @bridge()
  sls() {}

  @bridge()
  getSentry() {}

  @bridge()
  syncWechatUserH5OpenId() {}

  @bridge()
  on() {}

  @bridge()
  off() {}

  @bridge()
  emit() {}

  @bridge()
  once() {}
}

/**
 * 久久折小程序 bridge 适配器
 */
export class SqbBridgeAdapter extends BaseBridgeAdapter {
  constructor(bridge) {
    super(bridge, 'sqbBridge');
  }
}

/**
 * 收钱吧小程序 bridge 适配器
 */
export class MpBridgeAdapter extends BaseBridgeAdapter {
  constructor(bridge) {
    super(bridge, 'mpBridge');
  }

  @bridge()
  getEnvVersion() {}

  @bridge()
  setEnvFlag() {}

  @bridge()
  openOfflinePayView() {}

  @bridge()
  getCurrentPageUrl() {}

  @bridge()
  request() {}

  @bridge()
  identify() {}

  @bridge()
  getLoginInfo() {}

  @bridge()
  login() {}

  @bridge()
  logout() {}

  @bridge()
  setMeta() {}

  @bridge()
  getMeta() {}

  @bridge()
  removeMeta() {}

  @bridge()
  getCurrentPageQuery() {}

  @bridge()
  getLastNavigateQuery() {}

  @bridge()
  setMockLocation() {}

  @bridge()
  getMockLocation() {}

  @bridge()
  clearMockLocation() {}

  // below methods 兼容方法

  @bridge({ name: 'setShareAppMessage' })
  setShareAppMessage = (options = {}) => {
    let path = _.get(options, 'path');
    const [url, query = {}] = _.split(path, '?');
    path = mergeQuery(MP_ROOT_PATH, query);
    return {
      params: {
        ...options,
        path,
      },
    };
  };

  @bridge({ name: 'setNavigationBar' })
  setNavigation(args = {}) {
    const { homeButton } = args;
    if (!_.isNil(homeButton)) {
      return {
        params: {
          ...args,
          hideHomeButton: !homeButton,
        },
      };
    }
  }

  @bridge({ name: 'getMiniProgramUrl' })
  getMiniProgramUrl(opts) {
    const params = getMpBridgeUrlOptions.call(this, opts);
    return {
      params,
    };
  }

  @bridge({ local: true })
  getMiniProgramQuery() {
    const query = this.getCurrentPageQuery();
    if (_.isEmpty(query)) {
      return this.getLastNavigateQuery() || {};
    } else {
      return query;
    }
  }

  @bridge({ local: true })
  async scanCode() {
    const { result: url } = await sqb.scanCode({ onlyFromCamera: true, scanType: ['qrCode'] });
    this.navigateTo({
      plugin: {
        name: 'scanPlugin',
        component: 'page-router',
      },
      query: {
        q: url,
      },
    });
  }

  @bridge({ name: 'navigateTo' })
  navigateTo(opts) {
    const params = getMpBridgeUrlOptions.call(this, opts);
    return { params };
  }

  @bridge({ local: true })
  redirectTo(opts) {
    return new Promise(async (resolve, reject) => {
      try {
        opts = _.merge(opts, { replace: true });
        await this.navigateTo(opts);
        resolve();
      } catch (e) {
        reject(e);
      }
    });
  }

  @bridge({ name: 'getHillsStage' })
  getMiniProgramProfile() {
    const after = (hillsStage) => {
      return hillsStage === 'test' ? 'development' : 'production';
    };

    return {
      after,
    };
  }

  /**
   * 获取特性环境标识
   * !! local 参数为本地调试用，后续可删除
   * @returns
   */
  @bridge({ name: 'getEnvFlag', local: true })
  getMiniProgramEnv() {
    return '';
  }

  @bridge({ name: 'shence.track' })
  track() {
    const before = () => {
      console.log(`%c 🔌${this.__bridge_name__} 神策埋点需要在小程序对应项目注册事件`, 'color: blue');
    };
    return {
      before,
    };
  }

  @attr('slsLog')
  _sls = {
    // send: () => {},
    // pv: () => {}
  };

  @bridge({ local: true })
  async sls(topic, data = {}, __tags__ = {}) {
    // 兼容 mpBridge
    // const fn = this._sls[method];
    // _.isFunction(fn) && fn({ __topic__: topic, ...payload });

    // 本地实现
    const isSlsEnable = this.getMiniProgramConfig('SLS_ENABLE');
    const inWhiteList = this.getMiniProgramConfig('SLS_WHITE_LIST') || [];
    const storeId = this.getStoreInfo('id');

    function buildLog() {
      let token, userId, scene, storeName;

      token = this.getMiniProgramToken();
      const user = this.getMiniProgramUser();
      userId = _.get(user, 'id');
      scene = this.getMiniProgramScene();
      storeName = this.getStoreInfo('name');

      const log = _.reduce(
        data,
        (curr, value, key) => {
          if (_.isString(value)) {
            curr[key] = value;
          } else {
            curr[key] = JSON.stringify(value);
          }
          return curr;
        },
        {
          traceId,
          token,
          userId,
          scene,
          storeId,
          storeName,
          // __time__: Date.now(),
        },
      );
      if (isWeapp) {
        const accountInfo = sqb.getAccountInfoSync();
        const { envVersion, version } = _.get(accountInfo, 'miniProgram', {});
        _.assign(log, { envVersion, version });
      } else {
        _.assign(log, {
          version: 'alipay',
        });
      }
      return log;
    }

    if (isSlsEnable || inWhiteList.includes(storeId)) {
      const log = buildLog.call(this);
      __slsqueue__.push([log, topic, __tags__]);
    }
  }

  // TODO
  @bridge({ local: true })
  getSentry() {
    console.log(`%c 🔌${this.__bridge_name__} getSentry 需要做一些处理`, 'color: blue');
  }

  @bridge({ local: true })
  syncWechatUserH5OpenId(storeId, isForceWechatAuth, isShowSyncModal) {
    const { SYNC_WECHAT_USERINFO, WX_OAUTH_URL, WX_OAUTH_CALLBACK_URL } = Conf;

    const request = Request.request;

    return new Promise(async (resolve) => {
      storeId = storeId || this.getStoreInfo('storeId');
      const CACHE_KEY = '_WECHAT_OPENID_SYNCED';
      const FAILED_KEY = `_WECHAT_OPENID_FAILED:${storeId}`;
      const synced = storage(CACHE_KEY);
      if (
        isWeapp &&
        storeId &&
        SYNC_WECHAT_USERINFO &&
        !_.includes(synced, storeId) &&
        (isForceWechatAuth || !storage(FAILED_KEY))
      ) {
        // const { request } = Request.create(REQUEST_DEFAULT_CONFIGS);
        try {
          const config = await request('/v1/user/wechat/qrcodeConfig', { storeId });
          if (config) {
            // TODO
            // once(SYNC_UNION_EVENT, async (result) => {
            //   if (result) {
            //     const { appId, openId } = result;
            //     try {
            //       if (isForceWechatAuth) {
            //         await request('/v1/user/wechat/syncV2', { appId, openId }, null, 'POST');
            //       } else {
            //         await request('/v1/user/wechat/sync', { appId, openId }, null, 'POST');
            //       }
            //       storage(CACHE_KEY, _.concat(synced || [], storeId));
            //       return resolve(true);
            //     } catch (e) {
            //       resolve(false);
            //       if (isForceWechatAuth) {
            //         await sqb.showToast({ title: '同步收钱吧营销账号出错', icon: 'none' });
            //       }
            //     }
            //   }

            //   if (isForceWechatAuth && isShowSyncModal) {
            //     await sqb.showModal({
            //       title: '提示',
            //       content: '为了保障您的账户安全，需要获取您的用户信息，是否继续？',
            //       confirmText: '继续',
            //       success: async (resp) => {
            //         if (resp.confirm) {
            //           const hasSync = await syncWechatUserH5OpenId(storeId, true, true);
            //           resolve(hasSync);
            //         } else {
            //           resolve(false);
            //         }
            //       },
            //     });
            //   } else {
            //     resolve(false);
            //   }

            //   storage(FAILED_KEY, 1, dayjs().add(1, 'day').startOf('day'));
            // });
            const { appId } = config;
            const url = `${WX_OAUTH_URL}?appid=${appId}&redirect_user_uri=${encodeURIComponent(WX_OAUTH_CALLBACK_URL)}`;
            return this.navigateTo({
              path: '/pages/web/index',
              query: {
                url,
                _type: SYNC_UNION_EVENT,
              },
            });
          }

          storage(CACHE_KEY, _.concat(synced || [], storeId));
        } catch (e) {
          await sqb.showToast({ title: '同步微信信息出错', icon: 'none' });
          resolve(false);
        }
      }
      resolve(true);
    });
  }

  @bridge({ local: true })
  getTerminalInfo(key) {
    const terminal = camelCaseKeys(storage('terminalInfo'));
    if (terminal) {
      return key ? _.get(terminal, _.camelCase(key)) : terminal;
    }
  }

  @bridge({ local: true })
  async refreshTerminalInfo(url) {
    const terminal = storage('terminalInfo');
    const request = Request.request;
    url = url || _.get(terminal, '_url');
    if (url) {
      const refreshed = await request('/v1/qrcode/get', { url });
      storage('terminalInfo', _.assign(terminal, _.get(refreshed, 'data'), { _url: url }));

      return this.getTerminalInfo();
    }
  }

  @bridge({ local: true })
  getStoreInfo(key) {
    const store = camelCaseKeys(storage('storeInfo'));
    if (store) {
      return key ? _.get(store, _.camelCase(key)) : store;
    }
  }

  @bridge({ local: true })
  getMiniProgramConfig(key) {
    const configs = storage('APOLLO_CONFIG') || Conf || {};
    return key ? _.get(configs, key) : configs;
  }

  @bridge({ local: true })
  getMiniProgramScene() {
    return storage('scene') || 'manual';
  }

  @bridge({ local: true })
  getMiniProgramToken() {
    return storage('LOGIN.token');
  }

  @bridge({ local: true })
  getMiniProgramUser() {
    console.log('#####getMiniProgramUser ', this);
    const { USERINFO_FIELDS } = CONFIGS;
    const {
      wid,
      token,
      userId: id,
      cellphone: phoneNumber,
      thirdpartyUserId: openId,
      ctime,
      userInfo: _userInfo,
    } = storage('LOGIN') || {};
    const userInfo = _.pick(_userInfo, USERINFO_FIELDS);
    return { wid, id, token, phoneNumber, openId, ctime, ...userInfo };
  }

  @attr('on')
  _on = {
    // app: {
    //   show: () => {},
    //   hide: () => {},
    // },
    // page: {
    //   show: () => {},
    //   hide: () => {},
    //   unload: () => {},
    //   ready: () => {},
    //   shareAppMessage: () => {},
    //   shareTimeline: () => {},
    //   pullDownRefresh: () => {},
    //   reachBottom: () => {},
    //   resize: () => {},
    //   pageScroll: () => {},
    // },
  };

  @bridge({ local: true })
  on(name, handler, scope = 'page') {
    const fn = this._on[scope][name];
    _.isFunction(fn) && fn(handler);
  }

  @bridge()
  requestPayment() {}

  // @bridge({ local: true })
  // async getLocation(refresh) {
  //   let location = storage(LOCATION);
  //   const request = Request.request;
  //   const geoType = GEO_TYPE.toLowerCase();
  //   const { positioning = false } = getContext();
  //   const mockLocation = this.getMockLocation();
  //   try {
  //     if (mockLocation) location = mockLocation;
  //     else {
  //       if (!positioning && (refresh || !location)) {
  //         setContext({ positioning: true });
  //         // 获取地理位置
  //         location = await sqb.getLocation({ type: geoType });
  //       }
  //     }
  //
  //     const { latitude: lat, longitude: lon } = location;
  //     const { data: geo } = await request(true, '/v1/map/regeocode', _.omitBy({ lat, lon, geoType }, _.isNil));
  //     _.assign(location, { geo });
  //     storage(LOCATION, location);
  //     setContext({ location, positioning: false });
  //   } catch (e) {
  //     storage(LOCATION, false);
  //     setContext({ location: null, positioning: false });
  //     throw e;
  //   }
  //   return location;
  // }
}

export const bridgeAdapterMap = {
  BaseBridgeAdapter,
  SqbBridgeAdapter,
  MpBridgeAdapter,
};
