import _ from '@wosai/emenu-mini-lodash';

/**
 * bridge 适配装饰器
 * @param {*} params
 * @returns
 */
export const bridge = (params = {}) => {
  /**
   * @param name 所要调用 bridge 的名称
   * @param local 是否只执行本函数代码
   */
  const { name, local = false } = params;

  return (target, key, descriptor) => {
    const original = descriptor.value || descriptor.initializer.call(this);

    // const getContext = () => ({});

    if (_.isFunction(original)) {
      descriptor.value = function() {
        const __bridge__ = _.get(this, '__bridge__');
        const __bridge_name__ = _.get(this, '__bridge_name__');
        const result = original.apply(this, arguments);

        if (local) {
          // 只需要执行本地代码
          return result;
        }
        // 需要请求 bridge
        const inner = () => {
          const fnName = __bridge_name__ === 'sqbBridge' || !name ? key : name;

          /**
           * @return {before} 执行 bridge 前执行
           * @return {after} 执行 bridge 后执行
           * @return {params} 执行 bridge 的参数
           */
          const { before = _.noop, after, params } = result || {};

          if (__bridge__) {
            try {
              // Before
              const payload = before(arguments);

              // Bridge
              let fn = __bridge__[fnName],
                response = fn;
              if (_.isFunction(fn)) {
                const args = params || payload;
                if (args) {
                  response = fn(args);
                } else {
                  response = fn.apply(this, arguments);
                }
              }

              // After
              if (_.isFunction(after)) return after(response);
              else return response;
            } catch (error) {
              console.error(`${__bridge_name__} ${key} `, error);
            }
          }
        };
        return inner();
      };
    }

    return descriptor;
  };
};

export const attr = name => {
  return (target, key, descriptor) => {
    const newKey = name || key;

    descriptor.initializer = function() {
      const __bridge__ = _.get(this, '__bridge__');
      try {
        return __bridge__[newKey];
      } catch (error) {
        console.error(`${key}：`, error);
      }
    };
    return descriptor;
  };
};
