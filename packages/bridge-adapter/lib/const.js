export const routeMap = {
  'page-home': 'p0o2sfaiwk21',
  'page-search': 'p0268od3h4gy',
  'page-submit': '886iep4iu37w',
  'page-remark': 'l9tw9m8vgo2d',
  'page-address': 'u0w1f83ptdyv',
  'page-my-order': '71r5mxag59at',
  'page-order-detail': '8f4uj9ixh1c1',
  'page-order-pay': 'nhsvs86ckd8l',
  'page-cart': '19771xt9o39d',
  'page-user-count': 'ozsamyi986g2',
  'page-submit-order': '4ky3lsc29l30',
  'page-closing': 'hl7kiuilhx5t',
  'page-customer-pay': 'zrhi84mchgov',
  'page-combo': '6w1p4xnwqf5p',
  'page-aggregation': 'h6g14w38mf22',
  'page-router': 's11gfxefx0j6',
  'aggregation-list': 'wu7ytw6pi0bj',
  'page-campus': '9q25ti3259cn',
  'page-order-map': 'xa5wi02ur2vb',
  'page-orders': 'rzlo0v7142u1',
};

export const PLUGIN_HOME_PATH = 'E1QDEFPQUJWB';
export const bridgeNames = ['sqbBridge', 'mpBridge'];

export const miniProgramBridgeMap = {
  wxccbcac9a3ece5112: {
    bridgeName: 'mpBridge',
    bridgeAdapterName: 'MpBridgeAdapter',
  },
  wxd2f16468474f61b8: {
    bridgeName: 'sqbBridge',
    bridgeAdapterName: 'SqbBridgeAdapter',
  },
};

/**
 * TODO 需要使用 apollo 里面的值
 */
export const CONFIGS = {
  USERINFO_FIELDS: ['nickName', 'avatarUrl', 'gender', 'country', 'province', 'city'],
};

// 定位坐标标准
export const GEO_TYPE = 'gcj02';

export const MOCK_LOCATION = 'mockLocation';

export const LOCATION = 'location';

export const SYNC_UNION_EVENT = 'syncWechatUserH5OpenId';

export const MP_ROOT_PATH = `/${PLUGIN_HOME_PATH}/${routeMap['page-router']}/index`;
