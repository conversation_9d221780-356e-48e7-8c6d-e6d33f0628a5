//@ts-ignore
import _ from '@wosai/emenu-mini-lodash';
//@ts-ignore
import sqb from '@wosai/emenu-mini-native';
//@ts-ignore
import Request from '@wosai/emenu-mini-request';
//@ts-ignore
import { isAlipay, isWeixin, StorageUtils } from '@wosai/emenu-mini-utils';
//@ts-ignore
import CONFIG from '@wosai/emenu-mini-config';
import Conf from './conf';

const { storage } = StorageUtils;

const { HEADER_KEY_SCENE, HEADER_KEY_TOKEN, HEADER_KEY_TYPE, X_ENV_FLAG } = CONFIG;

const showToast = (msg) => {
  //@ts-ignore
  sqb.showToast({
    title: msg,
    icon: 'none',
    duration: 2000,
    mask: false,
  });
};

function needShowToast(task, code) {
  let result = true;
  // 所有预下单接口错误都不toast
  const preOrderAPIs = ['orders/pay', 'orders/round/pay'];
  const paths = ['orders/pay', 'orders/round/pay', 'api/106', 'v1/user/wechat/bindCellphone', 'v1/store/payConfig'];
  for (const path of paths) {
    if (_.get(task, 'options.url').includes(path)) {
      if (preOrderAPIs.includes(path)) return false;
      result = false;
      break;
    }
  }

  return result && Number(code) !== 40001 && Number(code) !== 0;
}

const toShowToast = async (response, task) => {
  const {
    data: { status, code = status },
  } = response || { data: {} };

  const {
    options: { url, method, header, data },
  } = task;

  console.log(`\n#######:::`);

  console.log(`url:`, url);
  console.log(`data:`, data);
  console.log(`header:`, header);
  console.log('response:', response);

  // 处理code为0的情况
  if ((code || _.isNumber(code)) && ![401, 200].includes(Number(code))) {
    const message = _.get(response.data, 'message') || _.get(response.data, 'errorMessage');

    _.merge(response.data, { message });

    if (needShowToast(task, code)) {
      message && showToast(message);
    }

    task.reject(response.data);
  }
  return response;
};

const parseUrl = (url) => {
  const urlArr = url.split('?');
  const _params = urlArr[1] && urlArr[1].split('&');
  const _url = urlArr[0];

  const params = _params && _params.length > 0 ? {} : null;
  if (_params) {
    _params.map((item) => {
      const a = item.split('=');
      params[a[0]] = a[1];
    });
  }

  return {
    url: _url,
    params,
  };
};

function setDefaultParams(options) {
  const defaultParams = {
    data: {
      compatible: true,
    },
  };
  let env = 'beta';
  //@ts-ignore
  const development = storage('profile') || 'production';
  if (development !== 'development') {
    env = 'prod';
  }

  const url = _.get(options, 'url', '');
  // 不需要传storeId的urls
  const ignoreStoreIdsUrls = [
    'v1/map/regeocode',
    'v1/campus',
    '/api/v1/gather/index',
    'v1/version/config',
    '/v1/store/list',
    '/v1/campus/nearby',
    'v1/report/user/consume/total',
    'v1/activity/cardsV2',
    '/v1/store/collection/user/all',
  ];

  const data = {
    env,
  };

  _.merge(defaultParams, { data });

  return _.merge(defaultParams, options);
}

const request = Request.create({
  getBaseUrl(pathname, extra) {
    //@ts-ignore
    const development = storage('profile') || 'production';
    const { LOG_URL, GATEWAY_URL, BASE_URL, MEAL_API_BASE_URL, API_SIX_URL } = Conf[development];
    const { type } = extra || {};
    const uf4bAPIList = [
      'orders/time',
      'carts',
      'orders/packAmount',
      'merchants/info',
      'stores/configs',
      'orders/packAmount',
      'items/search',
      'items/get',
      'orders/deliveryFee',
      'items/find',
      'items/recommend',
      'items/hotsale',
      'orders/getBySn',
      'orders/find',
      'orders/pay',
      'orders/again',
      'carts/reset',
      'stores/multiConfigs',
      'merchants/multiConfigs',
      'cashier/check/online',
      'peoplenum/update',
      'table/order',
      'orders/round/init',
      'orders/round/addGoods',
      'carts/round/check',
      'orders/round/pay',
      'round/paylock/cancel',
      '/v3/orders/latest',
      'delivery/rider/location',
      'api/v1/items/category',
      'api/v1/items/index',
      '/api/v1/gather/index',
      '/api/v1/merchants/isCollection',
      '/v1/carts/addAndRedeem',
      '/v1/items/material/recommend',
      '/api/v1/orders/bookTime',
      'v1/agreement/has',
      'v1/agreement/confirm',
      '/v1/jielong/query',
      '/v1/jielong/addCart',
      '/v1/jielong/check',
      '/v1/jielong/pay',
      '/v1/jielong/orderList',
      '/v1/jielong/genPoster',
      '/v1/jielong/getOrderBySn',
    ];
    const apiSixAPIList = [
      '/activity/goods/recommendList',
      // 二期
      '/v1/qrcode/plus',
      // '/v1/qrcode/convert', // 暂不适配
      '/v1/sqbMp/loginAsMember',
      '/v1/version/config',
      '/v1/store/detail',
      '/v1/store/listV2', // POST 接口路径修改为：/v1/store/listV2
      '/v1/store/stores',
      '/v1/store/payedStores',
      '/v1/store/districtHasStore',
      '/v1/store/wechatMchCode',
      '/v1/store/storePayWay',
      '/v1/store/terminal/list',
      '/v1/store/takeoutBusiness/status',
      '/v1/store/ufood/status',
      '/v1/store/collection/add',
      '/v1/store/collection/cancel',
      '/v1/store/collection/user/all',
      '/v1/store/storeSet',
      '/v1/merchant/tag',
      '/v1/merchant/tagWithCity',
      '/v1/merchant/ufood/status',
      '/v1/merchant/ufood/upayDelivery',
      '/v1/merchant/gray',
      '/v1/campus/nearbyList',
      '/api/merchant/v1/resource/getResource',
      // '/api/merchant/v1/resource/getOrderBanner',
      '/v1/user/login',
      '/v1/user/getUserInfo',
      '/v1/user/wechat/saveUserInfo',
      '/v1/user/alipay/saveUserInfo',
      '/v1/user/wechat/bindCellphone',
      '/v1/user/alipay/bindCellphone',
      '/v1/user/bindPhoneNumber',
      '/v1/user/sendAuthCode',
      '/v1/user/delete/self',
      '/v1/user/wechat/sync',
      '/v1/user/wechat/syncV2',
      '/v1/user/wechat/qrcodeConfig',
      '/v1/user/defaultAddressV2', // POST
      '/v1/user/allAddress',
      '/v1/user/defaultAddress', // GET
      '/v1/user/address',
      '/v1/user/deleteAddress',
      '/v1/user/addAgreementRecord',
      '/v1/user/hasAgreementRecord',
      // 一期
      '/v1/campus/userCollections',
      '/api/merchant/v1/campus/base',
      '/v1/campus/stores',
      '/v1/campus/hotSaleItems',
      '/v1/campus/storeCampusList',
      '/v1/campus/storeInAnyCampus',
      '/v1/campus/storeSupportCampusDelivery',
      '/v1/campus/wxQrCodeBanner',
      '/v1/campus/wxQrCodeShare',
      '/v1/customerService/url',
      '/v1/customerService/campusOrderUrl',
      '/v1/map/regeocode',
      '/v1/map/direction',
      '/v1/map/directionV2',
      '/v1/map/batchDirection',
      '/v1/map/batchDirectionV2',

      '/api/painter/v1/picture/jielongSharePicture',
      // '/api/merchant/v2/resource/getPopupsToC',
      // '/api/merchant/v2/resource/getPlanContentC',
      '/v3/storeRenovation/getStoreRenovation',
    ];

    // 日志1
    if (pathname.includes('track_imp')) return LOG_URL;
    // APISIX
    for (const api of apiSixAPIList) {
      if (api.includes(pathname)) return API_SIX_URL;
    }
    // UF4C
    for (const api of uf4bAPIList) {
      if (pathname.includes(api)) return MEAL_API_BASE_URL;
    }
    // JJZ-GATEWAY
    if (type === 'gateWay') return GATEWAY_URL;
    // CUSTOMER-GATEWAY
    return BASE_URL;
  },
  isDebug() {
    return false;
  },
  getRequestAdapter() {},
});

const addTaskToResponse = (response, task) => {
  _.set(response, 'data.xhr', task);
  return response;
};

const toShowNetworkErr = async (error, task) => {
  if (error && error.name === 'RequestFailedError') {
    //@ts-ignore
    sqb.hideLoading();
    // 重试最后第一次设置，如果还有异常，则提示相应的信息
    if (task.isRetrying && task.retryTimes === 0) {
      await showToast('网络异常，请检查网络后重试');
    }
  }
  // 如果是handle error， 一定要throw
  throw error;
};

function setDefaultHeader(options) {
  //@ts-ignore
  const token = storage('token');
  //@ts-ignore
  const envFlag = storage('txEnv');
  // let scene = null

  const defaultOptions = {
    method: 'GET',
    mode: 'cors',
    header: {
      //@ts-ignore
      APP_VERSION: __VERSION__,
      [HEADER_KEY_TOKEN]: token,
      [HEADER_KEY_SCENE]: 'manual',
      [HEADER_KEY_TYPE]: isWeixin() ? 'WECHAT' : 'ALIPAY',
      [X_ENV_FLAG]: envFlag,
    },
  };

  return _.merge(defaultOptions, options);
}

request.addBeforeHandler(setDefaultHeader.bind(request));
request.addBeforeHandler(setDefaultParams.bind(request));
request.addSuccessHandler(addTaskToResponse);
request.addSuccessHandler(toShowToast);

request.addErrorHandler(toShowNetworkErr);

export const get = (url, params, ...args) => {
  const method = _.find(args, _.isString) || 'GET';
  const headers = _.find(args, _.isString) || {};
  return request.request(url, params, headers, method);
};

export const post = (url, data, headers = {}) => {
  return request.request(url, data, headers, 'POST');
};

export { request };
