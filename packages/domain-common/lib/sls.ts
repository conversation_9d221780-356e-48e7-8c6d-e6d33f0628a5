//@ts-ignore
import _ from '@wosai/emenu-mini-lodash';
//@ts-ignore
import sqb from '@wosai/emenu-mini-native';

function isWeixin() {
  //@ts-ignore
  return typeof wx !== 'undefined' && !!wx.getSystemInfo;
}

export class Sls {
  param: Record<string, any> = null;
  enable: boolean = true;
  inWhiteList: Array<any> = [];
  queue: Array<any> = null;
  group: Array<any> = null;
  constructor() {
    this.param = {
      //@ts-ignore
      APP_VERSION: __VERSION__,
    };
    this.enable = true;
    this.inWhiteList = [];
    this.queue = [];
    this.group = [];
  }

  setParam(param = {}) {
    this.param = Object.assign(this.param, param);
  }

  setEnable(val: boolean) {
    this.enable = val;
  }

  setWhiteList(list: Array<string>) {
    list.forEach((l) => {
      if (this.inWhiteList.includes(l)) return;
      this.inWhiteList.push(l);
    });
  }

  send(topic: any, data = {}, __tags__ = {}) {
    const isSlsEnable = this.enable;
    const inWhiteList = this.inWhiteList;
    let { storeId } = this.param;

    if (isSlsEnable || (inWhiteList && inWhiteList.includes(storeId))) {
      const log = this.buildLog(data);
      this.queue.push([log, topic, __tags__]);
      this._send();
    }
  }

  _send() {
    const SLS_SEND_LOGS_TIMEOUT = 3000;
    let __slsgroup__ = this.group;
    let __slsqueue__ = this.queue;
    const toGroupLogs = () => {
      const slsqueue = __slsqueue__.splice(0);
      const groupLogs = _.chain(slsqueue)
        .groupBy((item) => item[1])
        .map((value, key) => {
          const __logs__ = _.map(value, (log) => log[0]);
          return {
            __topic__: key,
            __logs__,
          };
        })
        .value();
      __slsgroup__ = [...__slsgroup__, ...groupLogs];
    };

    setTimeout(() => {
      toGroupLogs();
      const params = __slsgroup__.shift();
      params && this.fire(params);
      this._send();
    }, SLS_SEND_LOGS_TIMEOUT);
  }

  // fire here !!
  fire({ __topic__, __logs__ }) {
    const url = 'https://mk-group.cn-hangzhou.log.aliyuncs.com/logstores/emenu-mini/track';
    // const storeId = _.get(this.getStoreInfo(), "id");

    const header = {
      'x-log-apiversion': '0.6.0',
      'x-log-bodyrawsize': '2048',
    };

    if (__topic__ && __logs__) {
      const body = {
        __topic__,
        __logs__,
        // __tags__: tags,
      };
      sqb.request({
        url,
        method: 'POST',
        data: body,
        header,
        fail: function() {
          // sentryInfo(err)
        },
      });
    }
  }

  buildLog(data: any) {
    const param = this.param;
    const log = _.reduce(
      data,
      (curr, value, key) => {
        if (_.isString(value)) {
          curr[key] = value;
        } else {
          curr[key] = JSON.stringify(value);
        }
        return curr;
      },
      param,
    );
    if (isWeixin()) {
      const accountInfo = sqb.getAccountInfoSync();
      const { envVersion, version } = _.get(accountInfo, 'miniProgram', {});
      _.assign(log, { envVersion, version });
    } else {
      _.assign(log, {
        version: 'alipay',
      });
    }
    return log;
  }
}
