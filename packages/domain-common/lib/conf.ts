const production = {
  BASE_URL: 'https://customer-gateway.shouqianba.com',
  LOG_URL: 'https://qr-log.shouqianba.com',
  GATEWAY_URL: 'https://jjz-api-gateway.shouqianba.com/api',
  MEAL_API_BASE_URL: 'https://uf4c-app.shouqianba.com',
  API_SIX_URL: 'https://gateway-smart.shouqianba.com',
  SENSORS_CONFIG: {
    name: 'sensors',
    server_url: 'https://shence.wosai-inc.com:8106/sa?project=production',
    send_timeout: 3000,
    use_client_time: false,
    show_log: false,
    allow_amend_share_path: true,
    autoTrack: {
      appLaunch: true,
      appShow: true,
      appHide: true,
      pageShow: true,
      pageShare: true,
    },
    is_plugin: false,
  },
};

const development = {
  BASE_URL: 'https://customer-gateway.iwosai.com',
  LOG_URL: 'https://qr-log.shouqianba.com',
  GATEWAY_URL: 'https://jjz-api-gateway.iwosai.com/api',
  MEAL_API_BASE_URL: 'https://uf4c-app.iwosai.com',
  API_SIX_URL: 'https://mk-apisix.iwosai.com',
  SENSORS_CONFIG: {
    name: 'sensors',
    server_url: 'https://shence.wosai-inc.com:8106/sa?project=default',
    send_timeout: 3000,
    use_client_time: false,
    show_log: false,
    allow_amend_share_path: true,
    autoTrack: {
      appLaunch: true,
      appShow: true,
      appHide: true,
      pageShow: true,
      pageShare: true,
    },
    is_plugin: false,
  },
};

const APP_ID = {
  WX: 'wx57e055d7d650fdff',
  MY: '2021002136689162',
};

export default { production, development, APP_ID };
