import { request } from './request';

function isWeixin() {
  //@ts-ignore
  return typeof wx !== 'undefined';
}

export const getAuthCode = () => {
  return new Promise((resolve, reject) => {
    if (isWeixin()) {
      //@ts-ignore
      wx.login({
        success: (res) => {
          resolve(res.code);
        },
        fail: () => {
          reject();
        },
      });
    } else {
      //@ts-ignore
      my.getAuthCode({
        success: (res) => {
          resolve(res.authCode);
        },
        fail: () => {
          reject();
        },
      });
    }
  });
};

export async function getGatherData(param = {}) {
  try {
    let code = await getAuthCode();

    let data = await request.request('/api/v1/gather/index', { ...param, code });

    if (data && ~~data.code === 200) {
      return data.data;
    }
  } catch (err) {
    console.log(err);
  }
}
