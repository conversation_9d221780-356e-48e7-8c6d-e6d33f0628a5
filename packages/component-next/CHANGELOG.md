# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [6.1.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v6.1.1) (2022-03-29)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))
- 修复收钱吧小程序会重复调用聚合接口（query 带 storeId, 导致扫码一直会扫一扫" ([c121eda](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c121edae80c0ab0c9f984fecf951361b542c5a4f))
- 恢复被 revert 的代码 ([e9572fb](https://git.wosai-inc.com/MK/emenu-mini-core/commits/e9572fb4dfe4d83b4e7d3ba1a54dc82711bcd722))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 同步最新代码 ([7f0ce88](https://git.wosai-inc.com/MK/emenu-mini-core/commits/7f0ce889e195eab8f6818fe17c38fae1d8b97e28))
- 将 emenu 放入 npm 包 ([b9fd193](https://git.wosai-inc.com/MK/emenu-mini-core/commits/b9fd193f52bd8052773f9c8fee29122b0bcc744e))
- 支持 props 传参数如： props: {value: null} ([c499c92](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c499c92488332fad47ceaf7f33dbe61d4ab1cfcd))
- 支持收钱吧小程序扫码点单组件化(删除领券组件） ([abfcd23](https://git.wosai-inc.com/MK/emenu-mini-core/commits/abfcd23898b246f77d606ee9270c9dab97d89d6f))
- 支持收钱吧小程序独立分包 ([39752d7](https://git.wosai-inc.com/MK/emenu-mini-core/commits/39752d73e752e07aa164a42ff8d372584b2f0626))
- 支持状态管理 ([cdf1c4b](https://git.wosai-inc.com/MK/emenu-mini-core/commits/cdf1c4b84fc7b71035364795545128e56b99d541))
- 更新喜茶主题（WIP) ([f309a5f](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f309a5f0f73f3a5aa7e47e02730a269fd7268fe4))
- 更新营销组件复制路径 ([3878646](https://git.wosai-inc.com/MK/emenu-mini-core/commits/38786462fbe26ebcb0d7a1ed2c62a39dd5842bbb))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))
- 重写推荐加料 ([107c28f](https://git.wosai-inc.com/MK/emenu-mini-core/commits/107c28f9c016ca230f163199275de38bbae27a85))

## [6.0.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v6.0.4) (2022-01-14)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))
- 修复收钱吧小程序会重复调用聚合接口（query 带 storeId, 导致扫码一直会扫一扫" ([c121eda](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c121edae80c0ab0c9f984fecf951361b542c5a4f))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 将 emenu 放入 npm 包 ([b9fd193](https://git.wosai-inc.com/MK/emenu-mini-core/commits/b9fd193f52bd8052773f9c8fee29122b0bcc744e))
- 支持 props 传参数如： props: {value: null} ([c499c92](https://git.wosai-inc.com/MK/emenu-mini-core/commits/c499c92488332fad47ceaf7f33dbe61d4ab1cfcd))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.9](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.9) (2021-09-30)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.8](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.8) (2021-09-30)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.7](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.7) (2021-09-29)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.6](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.6) (2021-09-29)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.5](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.5) (2021-09-29)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.4) (2021-09-28)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.3](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.3) (2021-09-28)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.6.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.6.1) (2021-09-28)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.495 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.494 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.493 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.492 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.491 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.490 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.489 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.488 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.487 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.486 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.485 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.484 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.483 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.482 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.481 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.480 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.479 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.478 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.477 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.476 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.475 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.475-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.474-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.473-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.472-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.471-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.470-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.469-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.468-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.467-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.466-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.465-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.464-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.463-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.462-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.461-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.460-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.459-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.458-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.457-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.456-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.455-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.454-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.453-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.452-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.451-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.450-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.449-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.448-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.447-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.446-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.445-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.444-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.443-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.442-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.441-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.440-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.439-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.438-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.437-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.436-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.435-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.434-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.433-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.432-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.431-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.430-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.429-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.428-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.427-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.426-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.425-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.424-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.423-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.422-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.421-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.420-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.419-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.418-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.417-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.416-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.415-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.414-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.413-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.412-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.411-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.410-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.409-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.408-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.407-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.406-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.405-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.404-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.403-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.402-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.401-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.400-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.399-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.398-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.397-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.396-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.395-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.394-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.393-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.392-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.391-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.390-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.389-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.388-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.387-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.386-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.385-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.384-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.383-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.382-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.381-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.380-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.379-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.378-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.377-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.376-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.375-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.374-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.373-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.372-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.371-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.370-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.369-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.368-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.367-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.366-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.365-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.364-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.363-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.362-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.361-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.360-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.359-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.358-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.357-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.356-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.355-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.354-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.353-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.352-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.351-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.350-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.349-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.348-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.347-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.346-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.345-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.344-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.343-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.342-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.341-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.340-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.339-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.338-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.337-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.336-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.335-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.334-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.333-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.332-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.331-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.330-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.329-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.328-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.327-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.326-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.325-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.324-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.323-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.322-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.321-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.320-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.319-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.318-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.317-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.316-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.315-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.314-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.313-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.312-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.311-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.310-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.309-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.308-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.307-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.306-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.305-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.304-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.303-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.302-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.301-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.300-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.299-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.298-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.297-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.296-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.295-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.294-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.293-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.292-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.291-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.290-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.289-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.288-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.287-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.286-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.285-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.284-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.283-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.282-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.281-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.280-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.279-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.278-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.277-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.276-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.275-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.274-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.273-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.272-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.271-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.270-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.269-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.268-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.267-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.266-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.265-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.264-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.263-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.262-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.261-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.260-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.259-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.258-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.257-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.256-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.255-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.254-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.253-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.252-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.251-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.250-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.249-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.248-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.247-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.246-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.245-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.244-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.243-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.242-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.241 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.241-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.240-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.239-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.238-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.237-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.236-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.235-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.234-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.233-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.232-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.231-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.230-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.229-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.228-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.227-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.226-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.225-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.224-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.223-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.222-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.221-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.220-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.219-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.218-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.217-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.216-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.215-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.214-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.213-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.212-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.211-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.210-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.209-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.208-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.207-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.206-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.205-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.204-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.203-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.202-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.201-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.200-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.199-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.198-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.197-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.196-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.195-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.194-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.193-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.192-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.191-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.190-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.189-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.188-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.187-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.186-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.185-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.184-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.183-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.182-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.181-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.180-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.179-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.178-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.177-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.176-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.175-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.174-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.173-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.172-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.171-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.170-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.169-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.168-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.167-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.166-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.165-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.164-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.163-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.162-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.161-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.160-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.159-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.158-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.157-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.156-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.155-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.154-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.153-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.152-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.151-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.150-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.149-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.148-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.147-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.146-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.145-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.144-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.143-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.142-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.141-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.140-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.139-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.138-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.137-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.136-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.135-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.134-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.133-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.132-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.131-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.130-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.129-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.128-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.127-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.126-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.125-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.124-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.123-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.122-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.121-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.120-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.119-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.118-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.117-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.116-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.115-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.114-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.113-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.112-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.111-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.110-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.109-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.108-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.107-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.106-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.105-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.104-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.103-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.102-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.101-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.100-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.99-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.98-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.97-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.96-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.95-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.94-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.93-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.92-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.91-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.90-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.89-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.88-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.87-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.86-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.85-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.84-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.83-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.82-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.81-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.80-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.79-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.78-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.77-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.76-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.75-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.74-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.73-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.72-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.71-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.70-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.69-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.68-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.67-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.66-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.65-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.64-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.63-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.62-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.61-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.60-alpha.0 (2021-09-27)

### Bug Fixes

- 点单插件-购物车】已经加购了必选品，还是提示“请点必选品” ([ac8b9f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ac8b9f3de3ba14ff3d3666ad79c5d839a746dd79))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.59-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.58-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.57-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.56-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.55-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.54-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.53-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.52-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.51-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.50-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 增加 PAGES 常量 ([2c542af](https://git.wosai-inc.com/MK/emenu-mini-core/commits/2c542aff12cde035635a16b5069f80a4de3d2874))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.49-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.48-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.47-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.46-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.45-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.44-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.43-alpha.0 (2021-09-27)

### Bug Fixes

- 【C-聚合页】店铺堂食，外卖均开启，进入门店点单页应有弹层，且默认选中堂食（前端传参缺少 storeSetId）#SMART-4956 ([63f2847](https://git.wosai-inc.com/MK/emenu-mini-core/commits/63f2847eaad9af62539039c2f3e1fdc72f077763)), closes [#SMART-4956](https://git.wosai-inc.com/MK/emenu-mini-core/issues/SMART-4956)

## 4.5.42-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.41-alpha.0 (2021-09-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 4.5.40-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.39-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))
- 调整使用统一日志 ([d23b964](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d23b9648bf5262749b14a32f167a28b9e9444732))

## 4.5.38-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.37-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.36-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.35-alpha.0 (2021-09-27)

### Features

- BIG UPDATE, HOOKS 有 BUGS， 使用 Promise 替换 HOOKS 处理数据流 ([4bd912c](https://git.wosai-inc.com/MK/emenu-mini-core/commits/4bd912ce46117768c86354cc4d8c11ce8118265c))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.34-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))
- 购物车为空时，不需要拉优惠信息 ([6d49ea2](https://git.wosai-inc.com/MK/emenu-mini-core/commits/6d49ea233351ab061bbde0a15d345f9599f42421))

## 4.5.33-alpha.0 (2021-09-27)

### Features

- 删除 discountPlugin, 并重写拉取优惠，支持不重复拉取优惠信息 ([dcbce2e](https://git.wosai-inc.com/MK/emenu-mini-core/commits/dcbce2e7e2818d14131dc3cc28d3fc3ead560109))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.32-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.31-alpha.0 (2021-09-27)

### Bug Fixes

- 点单插件-购物车】已经加购了必选品，还是提示“请点必选品” ([ac8b9f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ac8b9f3de3ba14ff3d3666ad79c5d839a746dd79))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.30-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.29-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.28-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.27-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.26-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.25-alpha.0 (2021-09-27)

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.24-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.23-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.22-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.21-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 增加 PAGES 常量 ([2c542af](https://git.wosai-inc.com/MK/emenu-mini-core/commits/2c542aff12cde035635a16b5069f80a4de3d2874))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.20-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.19-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## 4.5.17-alpha.0 (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.5.16](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.16) (2021-09-27)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.5.15](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.15) (2021-09-25)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.5.14](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.14) (2021-09-24)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))
- 调整使用统一日志 ([873aff8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/873aff8b3a6a7cc1d2c35c3ef59bd8c939624734))

## [4.5.13](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.13) (2021-09-23)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [4.5.11](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.11) (2021-09-23)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [4.5.8](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.8) (2021-09-23)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [4.5.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.4) (2021-09-22)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [4.5.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.5.1) (2021-09-22)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [4.4.2](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.4.2) (2021-09-22)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [4.3.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.3.1) (2021-09-22)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [4.2.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.2.1) (2021-09-19)

### Bug Fixes

- 修复优惠券不更新已选择状态 ([f3e976d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f3e976d7a3cc5f29d586c8c9184f9becf55f5814))

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [4.1.3](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.1.3) (2021-09-18)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [4.0.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v4.0.1) (2021-09-14)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [3.2.10](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.52...@wosai/emenu-mini-component-next@3.2.10) (2021-09-09)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.9](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.52...@wosai/emenu-mini-component-next@3.2.9) (2021-09-09)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.8](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.52...@wosai/emenu-mini-component-next@3.2.8) (2021-09-02)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.7](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.52...@wosai/emenu-mini-component-next@3.2.7) (2021-09-02)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.6](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.52...@wosai/emenu-mini-component-next@3.2.6) (2021-09-02)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.5](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.52...@wosai/emenu-mini-component-next@3.2.5) (2021-09-02)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))

## [3.2.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v3.2.4) (2021-09-01)

### Features

- add .npmrc to all ([90a2ca1](https://git.wosai-inc.com/MK/emenu-mini-core/commits/90a2ca1ad7aca07db1a66c70755f98ffbbf00113))
- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [3.2.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v3.2.1) (2021-08-27)

### Features

- add .npmrc to all ([de03e35](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de03e358d777596283acb27e9b78886352d90480))
- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [3.1.2](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.7...v3.1.2) (2021-08-27)

### Features

- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [3.0.52](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.51...@wosai/emenu-mini-component-next@3.0.52) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.51](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.50...@wosai/emenu-mini-component-next@3.0.51) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.50](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.49...@wosai/emenu-mini-component-next@3.0.50) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.49](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.48-alpha.0...@wosai/emenu-mini-component-next@3.0.49) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.48](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.48-alpha.0...@wosai/emenu-mini-component-next@3.0.48) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.48-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.47-alpha.0...@wosai/emenu-mini-component-next@3.0.48-alpha.0) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.47-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.46...@wosai/emenu-mini-component-next@3.0.47-alpha.0) (2021-08-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.46](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.45...@wosai/emenu-mini-component-next@3.0.46) (2021-08-26)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.45](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.44...@wosai/emenu-mini-component-next@3.0.45) (2021-08-25)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.44](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.43...@wosai/emenu-mini-component-next@3.0.44) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.43](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.42...@wosai/emenu-mini-component-next@3.0.43) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.42](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.41...@wosai/emenu-mini-component-next@3.0.42) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.41](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.40...@wosai/emenu-mini-component-next@3.0.41) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.40](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.39...@wosai/emenu-mini-component-next@3.0.40) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.39](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.38...@wosai/emenu-mini-component-next@3.0.39) (2021-08-24)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.38](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.37...@wosai/emenu-mini-component-next@3.0.38) (2021-08-23)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.37](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.36...@wosai/emenu-mini-component-next@3.0.37) (2021-08-23)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.36](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.35...@wosai/emenu-mini-component-next@3.0.36) (2021-08-20)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.35](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.34...@wosai/emenu-mini-component-next@3.0.35) (2021-08-20)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.34](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.33...@wosai/emenu-mini-component-next@3.0.34) (2021-08-20)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.33](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.32...@wosai/emenu-mini-component-next@3.0.33) (2021-08-20)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.32](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.31...@wosai/emenu-mini-component-next@3.0.32) (2021-08-20)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.31](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.30...@wosai/emenu-mini-component-next@3.0.31) (2021-08-18)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.30](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.29...@wosai/emenu-mini-component-next@3.0.30) (2021-08-18)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.29](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.28...@wosai/emenu-mini-component-next@3.0.29) (2021-08-18)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.28](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.27...@wosai/emenu-mini-component-next@3.0.28) (2021-08-18)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.27](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.26...@wosai/emenu-mini-component-next@3.0.27) (2021-08-17)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.26](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.25...@wosai/emenu-mini-component-next@3.0.26) (2021-08-17)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.25](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.24...@wosai/emenu-mini-component-next@3.0.25) (2021-08-17)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.24](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.23...@wosai/emenu-mini-component-next@3.0.24) (2021-08-17)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.23](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.22...@wosai/emenu-mini-component-next@3.0.23) (2021-08-17)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.22](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.21...@wosai/emenu-mini-component-next@3.0.22) (2021-08-16)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.21](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.20...@wosai/emenu-mini-component-next@3.0.21) (2021-08-16)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.20](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.19...@wosai/emenu-mini-component-next@3.0.20) (2021-08-16)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.19](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.18...@wosai/emenu-mini-component-next@3.0.19) (2021-08-16)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.18](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.17...@wosai/emenu-mini-component-next@3.0.18) (2021-08-16)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.17](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.16...@wosai/emenu-mini-component-next@3.0.17) (2021-08-13)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.16](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.15...@wosai/emenu-mini-component-next@3.0.16) (2021-08-09)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.15](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.14...@wosai/emenu-mini-component-next@3.0.15) (2021-08-09)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.14](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.13...@wosai/emenu-mini-component-next@3.0.14) (2021-08-09)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.13](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.12...@wosai/emenu-mini-component-next@3.0.13) (2021-08-06)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.12](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.11...@wosai/emenu-mini-component-next@3.0.12) (2021-08-06)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.11](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.10...@wosai/emenu-mini-component-next@3.0.11) (2021-08-06)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.10](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.9...@wosai/emenu-mini-component-next@3.0.10) (2021-08-06)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.9](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.8...@wosai/emenu-mini-component-next@3.0.9) (2021-08-06)

### Features

- request 增加 addBeforeHandlers, 主要处理参数 ([0a27cb8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/0a27cb892809dfa80b42e2c912d317191807da6b))

## [3.0.8](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.7...@wosai/emenu-mini-component-next@3.0.8) (2021-08-06)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.7](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@3.0.2...@wosai/emenu-mini-component-next@3.0.7) (2021-08-06)

## 3.0.6 (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.6](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v3.0.1...v3.0.6) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.2](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.100...@wosai/emenu-mini-component-next@3.0.2) (2021-08-05)

## 3.0.1 (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [3.0.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.4.8...v3.0.1) (2021-08-05)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复 this 不正确的问题 ([91a619d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/91a619d0ea3afcd4f6a0968fc833d43fe6c0c54d))
- 修复 this 不正确的问题 ([ef9b253](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ef9b25345a4a8a7ee9a4a922c439322a6dda346e))
- 修复 this 不正确的问题 ([ad400b3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ad400b33781c9faa185231890a4b8080990a25eb))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

### Features

- export sqbComponent ([10337f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/10337f39cb933cb2afb1b682f039243cd1501315))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))

## [1.1.100](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.99...@wosai/emenu-mini-component-next@1.1.100) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.99](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.98...@wosai/emenu-mini-component-next@1.1.99) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.98](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.97...@wosai/emenu-mini-component-next@1.1.98) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.97](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.96...@wosai/emenu-mini-component-next@1.1.97) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.96](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.95...@wosai/emenu-mini-component-next@1.1.96) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.95](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.94...@wosai/emenu-mini-component-next@1.1.95) (2021-08-05)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.94](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.93...@wosai/emenu-mini-component-next@1.1.94) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.93](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.92...@wosai/emenu-mini-component-next@1.1.93) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.92](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.91...@wosai/emenu-mini-component-next@1.1.92) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.91](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.90...@wosai/emenu-mini-component-next@1.1.91) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.90](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.88...@wosai/emenu-mini-component-next@1.1.90) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.89](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.88...@wosai/emenu-mini-component-next@1.1.89) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.88](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.87...@wosai/emenu-mini-component-next@1.1.88) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.87](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.86...@wosai/emenu-mini-component-next@1.1.87) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.86](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.85...@wosai/emenu-mini-component-next@1.1.86) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.85](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.84...@wosai/emenu-mini-component-next@1.1.85) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.84](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.84) (2021-08-04)

## 2.0.2 (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83) (2021-08-04)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.24](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.83-alpha.24) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.23](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.83-alpha.23) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.22](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.83-alpha.22) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.21](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.83-alpha.21) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.20](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.83-alpha.20) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.19](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.83-alpha.19) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.18](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.83-alpha.18) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.17](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.83-alpha.17) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.16](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.83-alpha.16) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.15](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.83-alpha.15) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.14](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.83-alpha.14) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.13](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.13) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.12](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.12) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 1.1.83-alpha.11 (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.10](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.10) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.9](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.9) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.8](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.8) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.7](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.7) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.6](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.6) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.5](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.5) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.4) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.3](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.3) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.2](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.2) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.83-alpha.0...@wosai/emenu-mini-component-next@1.1.83-alpha.1) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.83-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.82...@wosai/emenu-mini-component-next@1.1.83-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.82](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.82-alpha.0...@wosai/emenu-mini-component-next@1.1.82) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.82-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.81-alpha.0...@wosai/emenu-mini-component-next@1.1.82-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.81-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.80-alpha.0...@wosai/emenu-mini-component-next@1.1.81-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.80-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.79-alpha.0...@wosai/emenu-mini-component-next@1.1.80-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.79-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.78-alpha.0...@wosai/emenu-mini-component-next@1.1.79-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.78-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.77-alpha.0...@wosai/emenu-mini-component-next@1.1.78-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.77-alpha.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.76...@wosai/emenu-mini-component-next@1.1.77-alpha.0) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.76](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.75...@wosai/emenu-mini-component-next@1.1.76) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.75](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.74...@wosai/emenu-mini-component-next@1.1.75) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.74](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.73...@wosai/emenu-mini-component-next@1.1.74) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.73](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.72...@wosai/emenu-mini-component-next@1.1.73) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.72](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.71...@wosai/emenu-mini-component-next@1.1.72) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.71](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.70...@wosai/emenu-mini-component-next@1.1.71) (2021-08-03)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.70](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.69...@wosai/emenu-mini-component-next@1.1.70) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.69](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.61...@wosai/emenu-mini-component-next@1.1.69) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.68](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.61...@wosai/emenu-mini-component-next@1.1.68) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.67](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.61...@wosai/emenu-mini-component-next@1.1.67) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.66](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.61...@wosai/emenu-mini-component-next@1.1.66) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.65](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.61...@wosai/emenu-mini-component-next@1.1.65) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.64](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.61...@wosai/emenu-mini-component-next@1.1.64) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.63](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.61...@wosai/emenu-mini-component-next@1.1.63) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.62](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.61...@wosai/emenu-mini-component-next@1.1.62) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.61](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.61) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.60](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.60) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.59](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.59) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.58](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.58) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.57](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.57) (2021-08-02)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.56](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.56) (2021-08-01)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.55](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.55) (2021-08-01)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.54](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.53...@wosai/emenu-mini-component-next@1.1.54) (2021-08-01)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.53](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.49...@wosai/emenu-mini-component-next@1.1.53) (2021-08-01)

### Bug Fixes

- 修复 this 不正确的问题 ([91a619d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/91a619d0ea3afcd4f6a0968fc833d43fe6c0c54d))

## [1.1.52](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.49...@wosai/emenu-mini-component-next@1.1.52) (2021-08-01)

### Bug Fixes

- 修复 this 不正确的问题 ([91a619d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/91a619d0ea3afcd4f6a0968fc833d43fe6c0c54d))

## [1.1.51](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.49...@wosai/emenu-mini-component-next@1.1.51) (2021-08-01)

### Bug Fixes

- 修复 this 不正确的问题 ([91a619d](https://git.wosai-inc.com/MK/emenu-mini-core/commits/91a619d0ea3afcd4f6a0968fc833d43fe6c0c54d))

## [1.1.50](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.49...@wosai/emenu-mini-component-next@1.1.50) (2021-08-01)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.49](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.49) (2021-08-01)

### Bug Fixes

- 修复 this 不正确的问题 ([ef9b253](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ef9b25345a4a8a7ee9a4a922c439322a6dda346e))
- 修复 this 不正确的问题 ([ad400b3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ad400b33781c9faa185231890a4b8080990a25eb))

### Features

- export sqbComponent ([10337f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/10337f39cb933cb2afb1b682f039243cd1501315))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))

## [1.1.48](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.48) (2021-08-01)

### Bug Fixes

- 修复 this 不正确的问题 ([ad400b3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/ad400b33781c9faa185231890a4b8080990a25eb))

### Features

- export sqbComponent ([10337f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/10337f39cb933cb2afb1b682f039243cd1501315))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))

## [1.1.47](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.47) (2021-07-30)

### Features

- export sqbComponent ([10337f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/10337f39cb933cb2afb1b682f039243cd1501315))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))

## [1.1.46](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.46) (2021-07-29)

### Features

- export sqbComponent ([10337f3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/10337f39cb933cb2afb1b682f039243cd1501315))
- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))

## [1.1.45](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.45) (2021-07-29)

### Features

- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))

## [1.1.44](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.44) (2021-07-29)

### Features

- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))

## [1.1.43](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.43) (2021-07-29)

### Features

- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))

## [1.1.42](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.42) (2021-07-29)

### Features

- 临时修复 adapter ([1ac1ad8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1ac1ad859e564cac6568d80f8a5b6d764f2ea4b4))

## [1.1.41](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.41) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.40](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.40) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.39](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.39) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.38](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.38) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.37](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.37) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.36](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.36) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.35](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.35) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.34](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.34) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.33](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.33) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.32](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.32) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.31](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.31) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.30](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.30) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.29](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.29) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.28](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.28) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.27](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.26...@wosai/emenu-mini-component-next@1.1.27) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.26](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.15...@wosai/emenu-mini-component-next@1.1.26) (2021-07-29)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.25](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.1.15...@wosai/emenu-mini-component-next@1.1.25) (2021-07-28)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.15](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.15) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.1.14](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.14) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.1.13](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.13) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.1.12](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.12) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.1.11](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.11) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.1.10](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.10) (2021-07-27)

### Bug Fixes

- fix error version ([bb3acfd](https://git.wosai-inc.com/MK/emenu-mini-core/commits/bb3acfdc08e33afc67f43b691cb7d49efc90c048))
- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.1.9](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.9) (2021-07-27)

### Bug Fixes

- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.1.8](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.8) (2021-07-27)

### Bug Fixes

- 修复错误的组件引用 ([29a3016](https://git.wosai-inc.com/MK/emenu-mini-core/commits/29a3016329b00aa3f9ec40ccea487684fbbda0b5))
- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.1.7](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.7) (2021-07-27)

### Bug Fixes

- 修复错误的组件引用 ([1688fe8](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1688fe8adde833196afb627358bd93422bb678d2))
- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.1.6](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.6) (2021-07-27)

### Bug Fixes

- 修复错误的组件引用 ([d0dd187](https://git.wosai-inc.com/MK/emenu-mini-core/commits/d0dd1875415c487a9159325903b2b63862c58dcb))
- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.1.5](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.5) (2021-07-27)

### Bug Fixes

- 修复错误的组件引用 ([de682a5](https://git.wosai-inc.com/MK/emenu-mini-core/commits/de682a5fda550bb549a708ce7f329c5fa991bee9))

## [1.1.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.4) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.3](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.3) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.1.2](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.1.2) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.0.9](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.0.9) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.0.8](https://git.wosai-inc.com/MK/emenu-mini-core/compare/@wosai/emenu-mini-component-next@1.0.6...@wosai/emenu-mini-component-next@1.0.8) (2021-07-27)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 1.0.6 (2021-07-26)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 1.0.5 (2021-07-26)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 1.0.4 (2021-07-26)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 1.0.3 (2021-07-26)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 1.0.2 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## 1.0.1 (2021-07-23)

**Note:** Version bump only for package @wosai/emenu-mini-component-next

## [1.4.5](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.4.4...v1.4.5) (2021-07-15)

**Note:** Version bump only for package @wosai/emenu-mini-component

## [1.4.4](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.4.3...v1.4.4) (2021-07-15)

**Note:** Version bump only for package @wosai/emenu-mini-component

## [1.4.3](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.4.2...v1.4.3) (2021-07-15)

**Note:** Version bump only for package @wosai/emenu-mini-component

## [1.4.2](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.4.1...v1.4.2) (2021-07-15)

**Note:** Version bump only for package @wosai/emenu-mini-component

## [1.4.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.2.7...v1.4.1) (2021-07-15)

### Features

- 增加 lodash,service 等 npm 的包 ([960f9c3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/960f9c30d74846bd8f10c67d218c7f3d775d2673))
- 更新 request ([fc9c75f](https://git.wosai-inc.com/MK/emenu-mini-core/commits/fc9c75f77d858134064270f676686084c16242e3))
- 更新 request ([f0485bc](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f0485bc904ad2f0ac007373db447e2125ea12718))

# [1.4.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.2.7...v1.4.0) (2021-07-15)

### Features

- 增加 lodash,service 等 npm 的包 ([960f9c3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/960f9c30d74846bd8f10c67d218c7f3d775d2673))
- 更新 request ([f0485bc](https://git.wosai-inc.com/MK/emenu-mini-core/commits/f0485bc904ad2f0ac007373db447e2125ea12718))

# [1.3.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.2.7...v1.3.0) (2021-07-15)

### Features

- 增加 lodash,service 等 npm 的包 ([960f9c3](https://git.wosai-inc.com/MK/emenu-mini-core/commits/960f9c30d74846bd8f10c67d218c7f3d775d2673))

## [1.2.1](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.2.0...v1.2.1) (2021-01-16)

### Features

- 完善各个模块使用文档 ([2a07ba0](https://git.wosai-inc.com/MK/emenu-mini-core/commits/2a07ba0be33055c0d3348e9eca3f261738a2fb34))

# [1.2.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.1.0...v1.2.0) (2021-01-15)

### Features

- add request(only wx) ([54cca76](https://git.wosai-inc.com/MK/emenu-mini-core/commits/54cca7673c3628403bf937f51f4b039d3a27e98d))

# [1.1.0](https://git.wosai-inc.com/MK/emenu-mini-core/compare/v1.0.1...v1.1.0) (2021-01-15)

### Features

- 增加 utils、native、component、redux ([1062201](https://git.wosai-inc.com/MK/emenu-mini-core/commits/1062201e4f7c1426901dfeab32685fd1e7c52acf))
