# @wosai/emenu-mini-component

### 描述

主要为开发支付宝、微信小程序插件，统一组件传参

### 使用

```javascript
import sqbComponent from '@wosai/emenu-mini-component'

sqbComponent({
    /*
     *   属性的定义方式
    */
    props:{
        /*
        * type:属性的类型结构
        * value:初始值，可不填
        */
        text:{
             type:String | Number | Array | Object | Boolean,
             value: "value"
        },

        /*
         * 属性里传入回调，必须以on开头,驼峰写法
        */
        onChange:()=>{}

    },

    /*
    * 目前支付宝只支持pros里的数据监听
    */
    observers:{
        'text':function(newValue) {}
    }
    /*
     *   td 为节流和防抖配置，若在此处配置了，
     *   则在method里定义的方法会进行相应处理
    */
    td:{
        /*
         *   type:节流 | 防抖，
         *   delay:延迟毫秒数，不写默认为800ms
        */
        'clickUser':{
            type:'throttle' | 'debounce',
            delay:800
        }
    },
    /*
        store为监听全局数据变量的监听
        支持'a'或者'a.b'格式监听
        如果'a.b'被进行修改，则只会触发'a.b’相应的回调，不会触发'a'监听的函数
    */
    store:{
        'good':function(newValue) {
           // newvalue 返回的是good的全局变量
        },
        'good.name':function(newValue){
           // newvalue 返回的是good的全局变量
        }
    },

    /*
        定义方式和原生一样
    */
    data:{
        name:'shouqianba'
    },

    /*
        定义方式和原生一样
    */
    methods:{
        clickUser(){
            this.$emit('change',{name:'shouqianba'})
        }
    },
    /*
     *   微信对应attached,
     *   支付宝对应onInit
    */
    created(){

    }
    /*
    * 微信对应ready,
    * 支付宝对应didMount
    */

    mounted(){

    },

    /*
    * 微信对用detached
    * 支付宝对应didUnmount
    */

    destroyed:{

    }
})
```

### 父子组件通信

#### 子组件

```
    methods:{
        clickUser:function(){
            this.$emit('change',{name:'收钱吧'})
        }
    }

```

#### 父组件

```
    支付宝axml:
    <SubComponent onChange='changeCallback'/>

    微信axml:
     <SubComponent bind:change='changeCallback'/>

    methods:{
        changeCallback:function(value) {
            这里的value 值为 {name:'收钱吧'}
        }
    }
```

### 全局数据监听

#### 组件使用方式

```
    store:{
        'userInfo':function(newValue){

        },
        'userInfo.name':function(newValue) {

        }
    }
```

#### 类的使用方式

```
import {
    connectToInstance
} from 'xxx'
class User{

    /*
        全局state会定义到类的数据属性里,
        state会进行new proxy的深度监听，
        当这里有改变的时候，会通知相应的依赖组件
    */
    editUserInfo(){
        this.state.userInfo = {name:'xukaijie'} // 这种方式触发userInfo的监听
         this.state.userInfo.name = 'shouqianba' //这种方式触发userInfo.name的监听
    }
}

export default connectToInstance(new User())

```

### 属性中函数名称的保留字段

以下名称是微信和支付宝的原生事件的类型，不能在 props 定义该名称的函数

```
  'touchstart',
  'touchmove',
  'touchcancel',
  'touchend',
  'tap',
  'longpress',
  'longtap',
  'transitionend',
  'animationstart',
  'animationiteration',
  'animationend',
  'touchforcechange',
  'scroll'

    错误定义方式：
    props:{
        onScroll:()=>{} // scroll 为原生的事件类型，我们不能使用
    }

```

### 防抖和节流

为了方便使用者不用手动对函数进行防抖节流处理，框架进行了配置处理，如下使用：

```
td:{
    'clickUser':{
        type:'debounce',
        delay:300 // 不填默认为800ms
    }
},

methods:{
    clickUser:function(){}
}
```

当在 td 里定义了 clickUser 防抖，则在 methods 里的函数就具备了相应的防抖能力。

### 贡献者
@徐开洁 