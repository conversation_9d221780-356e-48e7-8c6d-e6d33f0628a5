import emitConfig from "./emit";
import { isWeixin, isAlipay,mapKeys } from '../helper/util';
import { aliComponentMap, weappComponentMap } from '../helper/const';
let arr = [emitConfig];

function mapMixins(mixins) {
  return mixins.map(config => {
    let nativeConfig = {};
    let map;
    if (isWeixin()) map = weappComponentMap;else if (isAlipay()) map = aliComponentMap;
    mapKeys(config, nativeConfig, map);
    return nativeConfig;
  });
}
// TODO：为
export function createAlipayBasicMixins(mixins) {
  let merge = mixins.concat(arr)
  merge = mapMixins(merge)
  return merge;
}
export function createWeappBasicMixins(mixins) {
  let merge = mixins.concat(arr)
  merge = mapMixins(merge)
  let behaviros = merge.map(a => Behavior(a));
  return behaviros;
}



export function useMixIns(params) {
  return isWeixin() ? Behavior(params) : Mixin(params)

}