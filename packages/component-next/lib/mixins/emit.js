import { isWeixin, isAlipay } from '../helper/util';

import { nativeEventList } from '../helper/const';

function checkEventNameValid(name) {
  if (nativeEventList.indexOf(name) !== -1) {
    throw new Error(`error: event name ${name} is native , `);
  }
}

export default {
  methods: {
    $emit(eventName, args) {
      checkEventNameValid(eventName);
      if (isWeixin()) {
        this.triggerEvent(eventName, args);
      } else if (isAlipay()) {
        eventName = eventName.replace(/\b(\w)(\w*)/g, function($0, $1, $2) {
          return $1.toUpperCase() + $2;
        });

        // 将参数进行分装
        const timeStamp = +new Date();
        const eventObj = {
          type: eventName,
          timeStamp,
          detail: args,
        };
        var funcName = `on${eventName}`;
        if (this.props[funcName]) {
          this.props[funcName](eventObj);
        }
      }
    },
  },
};
