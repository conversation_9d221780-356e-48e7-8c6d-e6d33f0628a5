import { ENV_TYPE } from './const';

export function getTag(value) {
  return Object.prototype.toString.call(value);
}

export function isArray(value) {
  return getTag(value) === '[object Array]';
}

export function isObject(value) {
  return getTag(value) === '[object Object]';
}

export function isFunction(value) {
  return getTag(value) === '[object Function]';
}

export function deepArrayEqual(prev, next) {
  const prevLen = prev.length;
  const nextLen = next.length;
  if (prevLen !== nextLen) return false;
  for (var i = 0; i < prevLen; i++) {
    const prevValue = prev[i];
    const nextValue = next[i];
    if (!deepEqual(prevValue, nextValue)) return false;
  }
  return true;
}

export function deepObjectEqual(prev, next) {
  const prevKeys = Object.keys(prev);
  const nextKeys = Object.keys(next);
  if (prevKeys.length !== nextKeys.length) return false;

  for (var key in prev) {
    if (!hasOwnProperty.call(next, key)) return false;
    const prevValue = prev[key];
    const nextValue = next[key];
    if (!deepEqual(prevValue, nextValue)) return false;
  }

  return true;
}

// 是否相等
export function deepEqual(prev, next) {
  const prevTag = getTag(prev);
  const nextTag = getTag(next);
  if (prevTag !== nextTag) return false;
  if (isArray(prev)) {
    return deepArrayEqual(prev, next);
  }
  if (isObject(prev)) {
    return deepObjectEqual(prev, next);
  }
  return prev === next;
}

export function fill(source, name, replacement) {
  const original = source[name];
  const wrapped = replacement(original);

  source[name] = wrapped;
}

export function mapKeys(source, target, map) {
  Object.keys(map).forEach((key) => {
    if (source[key]) {
      target[map[key]] = source[key];
    }
  });
}

// 页面的key都要给过去
export function mapPageKeys(source, target, map) {
  Object.keys(source).forEach((key) => {
    if (map[key]) {
      target[map[key]] = source[key];
    } else {
      target[key] = source[key];
    }
  });
}

export function throttle(func, wait, options) {
  var timeout, context, args, result;
  var previous = 0;
  if (!options) options = {};

  var later = function () {
    previous = options.leading === false ? 0 : new Date();
    timeout = null;
    result = func.apply(context, args);
    if (!timeout) context = args = null; // 显示地释放内存，防止内存泄漏
  };

  var throttled = function () {
    var now = new Date();
    if (!previous && options.leading === false) previous = now;
    var remaining = wait - (now - previous);
    context = this;
    args = arguments;
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = now;
      result = func.apply(context, args);
      if (!timeout) context = args = null;
    } else if (!timeout && options.trailing !== false) {
      timeout = setTimeout(later, remaining);
    }
    return result;
  };

  throttled.cancel = function () {
    clearTimeout(timeout);
    previous = 0;
    timeout = context = args = null;
  };

  return throttled;
}

export const compThrottled = (func, delay = 800, options = {}) => {
  return throttle(func, delay, Object.assign({ leading: true, trailing: false }, options));
};

export const debounce = (fn, wait) => {
  const callback = fn;
  let timerId = null;

  function debounced() {
    // 保存作用域
    const context = this;
    // 保存参数，例如 event 对象
    const args = arguments;

    clearTimeout(timerId);
    timerId = setTimeout(function () {
      callback.apply(context, args);
    }, wait);
  }

  // 返回一个闭包
  return debounced;
};

let _env = null;

export function getEnv() {
  if (_env) return _env;

  if (typeof wx !== 'undefined' && wx.getSystemInfo) {
    _env = ENV_TYPE.WEAPP;
    return ENV_TYPE.WEAPP;
  }

  if (typeof my !== 'undefined' && my.getSystemInfo) {
    _env = ENV_TYPE.ALIPAY;
    return ENV_TYPE.ALIPAY;
  }

  return 'Unknown environment';
}

export function isWeixin() {
  return getEnv() === ENV_TYPE.WEAPP;
}

export function isAlipay() {
  return getEnv() === ENV_TYPE.ALIPAY;
}

export function isEmpty(obj) {
  if (!obj) return true;
  for (var key in obj) {
    return false;
  }

  return true;
}

/**
 * Convert an Array-like object to a real Array.
 */
export function toArray(list, start) {
  start = start || 0;
  let i = list.length - start;
  const ret = new Array(i);
  while (i--) {
    ret[i] = list[i + start];
  }
  return ret;
}

export function checkPropValid(prop, key) {
  if (typeof prop !== 'object') {
    throw new Error(`prop ${key} is not object`);
  }
  const type = prop && prop.type;
  if (![String, Number, Boolean, Object, Array, null].includes(type)) {
    throw new Error(`props ${key} invalid type  ${type}, please check it`);
  }
}

function _mergeObject(target, source) {
  for (let key in source) {
    target[key] = source[key];
  }
}

// merge component options
export function mergeOptions(target, source) {
  function _merge(t, s) {
    for (let key in s) {
      if (t[key]) {
        _mergeObject(t[key], s[key]);
      } else {
        t[key] = s[key];
      }
    }
  }

  if (isArray(source)) {
    source.forEach((s) => {
      _merge(target, s);
    });
  } else {
    _merge(target, source);
  }
}

export function initTd(options, vm) {
  let { td } = options;
  for (var method in td) {
    if (!vm[method]) {
      console.warn(`comp throttle ${method} not found in component,please check`);
      continue;
    }

    let key = method;
    let value = td[method];
    if (typeof value !== 'object') {
      throw new Error(`${key} define error`);
    }

    const type = value.type;
    const delay = value.delay || 800;
    const tdOptions = value.options;
    const defineMethodName = `__sqbTd${method}`;
    const defineMethod = (vm[defineMethodName] = vm[method]);

    if (type !== 'throttle' && type !== 'debounce') {
      throw new Error(`${key} define type is  error`);
    }
    let func = type === 'throttle' ? compThrottled : debounce;
    vm[method] = func(defineMethod.bind(vm), delay, tdOptions);
  }
}

export function initMixins(comp, creator) {
  let Ctor = comp.constructor;
  let { $options } = Ctor;
  let globalMixins = $options.mixins || [];
  let options = comp.options;
  let userMixins = options.mixins || [];
  let merged = globalMixins.concat(userMixins);
  options.mixins = creator(merged);

  // 合并微信/支付宝原生behaviors/mixins
  if (options.behaviors) {
    options.mixins = options.mixins.concat(options.behaviors);
  }

  return;
}

export function _mergeProp(props, key, _prop) {
  props[key] = {
    type: _prop['type'],
    value: _prop['value'],
  };
}

export function mergeGlobalProps(comp) {
  let Ctor = comp.constructor;
  let {
    $options: { props: $props },
  } = Ctor;
  let options = comp.options;
  if (!options.props) options.props = {};
  let { props = {} } = options;
  for (let key in $props) {
    let _prop = $props[key];
    _mergeProp(props, key, _prop);
  }
  return;
}

export function bindMethods(options, vm) {
  let methods = options.methods || {};

  for (const method of Object.keys(methods)) {
    if (vm[method] && typeof vm[method] === 'function') vm[method] = vm[method].bind(vm);
  }
}

export function proxyDataWidthProps(options) {
  for (var key in options.props) {
    let _key = key;
    Object.defineProperty(this.data, _key, {
      configurable: true,
      get: () => {
        return this.props[_key];
      },
    });
  }
}

let uid = 1;

export const getUid = () => {
  return uid++;
};

/**
 * 
  {
    'setDataName' :{
      key:"name",
      value:xxx
    }
  }
 */

export function initQuickState(instance, options = {}) {
  if (!options.useState) return;

  let state = options.useState;

  if (instance.__hasInitQuickState) return;

  let methods = Object.keys(state);

  methods.forEach((method) => {
    instance[method] = function () {
      let body = state[method];
      let { key, value } = body;
      if (!key) return;
      let param = {};
      param[key] = typeof value === 'function' ? value.call(instance, ...arguments) : value;
      instance.setData(param);
    }.bind(instance);
  });

  instance.__hasInitQuickState = true;
}
