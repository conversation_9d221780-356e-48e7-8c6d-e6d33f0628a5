export const aliComponentMap = {
  data: 'data',
  props: 'props',
  mixins: 'mixins',
  didUpdate: 'didUpdate',
  methods: 'methods',
  created: 'onInit',
  mounted: 'didMount',
  destroyed: 'didUnmount',
  error: 'onError',
  relations: 'relations',
  options: 'options',
};
export const weappComponentMap = {
  data: 'data',
  props: 'properties',
  mixins: 'behaviors',
  observers: 'observers',
  relations: 'relations',
  methods: 'methods',
  created: 'attached',
  mounted: 'ready',
  destroyed: 'detached',
  classes: 'externalClasses',
  error: 'error',
  options: 'options',
};

export const ENV_TYPE = {
  WEAPP: 'WX',
  ALIPAY: 'MY',
};

export const nativeEventList = [
  'touchstart',
  'touchmove',
  'touchcancel',
  'touchend',
  'tap',
  'longpress',
  'longtap',
  'transitionend',
  'animationstart',
  'animationiteration',
  'animationend',
  'touchforcechange',
  'scroll',
];

export const aliPageMap = {
  data: 'data',
  created: 'onLoad',
  mounted: 'onReady',
  show: 'onShow',
  hide: 'onHide',
  destroyed: 'onUnload',
};
export const weappPageMap = {
  data: 'data',
  created: 'onLoad',
  mounted: 'onReady',
  destroyed: 'onUnload',
  show: 'onShow',
  hide: 'onHide',
};
