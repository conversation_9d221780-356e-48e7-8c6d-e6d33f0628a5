import initUse from './use';
import initMixin from './mixin'
import { isObject, isWeixin, isAlipay, mapKeys,_mergeProp } from '../helper/util';

import { aliComponentMap, weappComponentMap } from '../helper/const';

import WeappPrototype from '../platform/wx/index';

import AlipayPrototype from '../platform/my/index';

function Instance(options) {
  if (!isObject(options)) {
    throw new Error(`sqbComponent params must be object`);
  }
  this.options = options;
  this.__init();
}
if (isWeixin()) {
  Instance.prototype = Object.create(WeappPrototype);
} else if (isAlipay()) {
  Instance.prototype = Object.create(AlipayPrototype);
} else {
  Instance.prototype = Object.create(WeappPrototype);
}

Instance.prototype.constructor = Instance;

Instance.prototype.getConfig = function() {
  let sqbOptions = {};
  let options = this.options;

  let map;
  if (isWeixin()) map = weappComponentMap;
  else if (isAlipay()) map = aliComponentMap;
  mapKeys(options, sqbOptions, map);
  return sqbOptions;
};

// static global options ,will be merged with new Instance option
Instance.$options = {
  props:{
    customStyle:{
      type:String,
      value:''
    }
  }
};

Instance.useProps = function({props = {}}) {
    for (let key in props) {
      let _prop = props[key]
      _mergeProp(Instance.$options.props,key,_prop)
    }
} 


initMixin(Instance);
initUse(Instance);

export default Instance;
