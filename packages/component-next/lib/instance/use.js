import { toArray } from '../helper/util';

// init use function
// mount use function to Component prototype
// the function is same Vue.use()
export default function initUse(Instance) {
  Instance.use = function(plugin) {
    if (!plugin) return this;
    const installedPlugins = this._installedPlugins || (this._installedPlugins = []);
    if (installedPlugins.indexOf(plugin) > -1) {
      return this;
    }

    // additional parameters
    const args = toArray(arguments, 1);
    args.unshift(this);
    if (typeof plugin.install === 'function') {
      plugin.install.apply(plugin, args);
    } else if (typeof plugin === 'function') {
      plugin.apply(null, args);
    }
    installedPlugins.push(plugin);
    return this;
  };
}
