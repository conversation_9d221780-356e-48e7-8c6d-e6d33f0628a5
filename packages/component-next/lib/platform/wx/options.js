

import {
    mergeGlobalProps,
    checkPropValid,
    initTd,
    fill,
    bindMethods,
    initQuickState,
  } from '../../helper/util';
import { BaseOptions } from '../options.js';

export class WxOptions extends BaseOptions{

    init() {
        this.initProps();
        this.initMixin();
        this.initCreated();
    }

    initProps(){

        let { instance,options } = this;
        if (instance)  mergeGlobalProps(instance,options);
        let { props = {} } = options;
        for (var key in props) {
          if (typeof props[key] === 'function') {
            delete props[key];
            continue;
          }
          checkPropValid(props[key], key);
        }
      }


    

     initCreated() {
        let options = this.options;
        let self = this;
        fill(options, 'created', (origin) => {
          return function(...args) {
            self.processBeforeRun(this,self.processBeforeRunner);
            if (origin) return origin.call(this, ...args);
          };
        });
    }


    processBeforeRunner(vm) {        
        let { options } = this;
        bindMethods(options, vm);
        initQuickState(vm, options);
        initTd(options, vm);
 
    }
}