import {
  checkPropValid,
  initTd,
  fill,
  initMixins,
  mergeGlobalProps,
  bindMethods,
  initQuickState,
} from '../../helper/util';

import { createWeappBasicMixins } from '../../mixins/index';

import{
  WxOptions
} from "./options"
// weapp prototype
let WeappPrototype = {
  __init: function() {
    let { options } = this;
    if (options.V2) {
      let op = new WxOptions({
        options:this.options,
        instance:this,
        parent:null
      })
      op.init();
      this.options = op.options;

    }else {
      this.initProps();
      this.initMixin();
      this.initCreated();
    }

  },
  initProps: function() {
    mergeGlobalProps(this);
    let { props = {} } = this.options;
    for (var key in props) {
      if (typeof props[key] === 'function') {
        delete props[key];
        continue;
      }
      checkPropValid(props[key], key);
    }
  },

  initMixin: function() {
    initMixins(this, createWeappBasicMixins);
  },

  initCreated() {
    let options = this.options;
    fill(options, 'created', (origin) => {
      return function(...args) {
        bindMethods(options, this);
        initQuickState(this, options);
        initTd(options, this);
        if (origin) return origin.call(this, ...args);
      };
    });
  },
};

export default WeappPrototype;
