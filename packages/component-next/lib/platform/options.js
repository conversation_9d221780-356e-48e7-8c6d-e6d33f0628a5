
import { aliComponentMap, weappComponentMap } from '../helper/const';
import { isWeixin, isAlipay,mapKeys } from '../helper/util';

import emitConfig from "../mixins/emit";

export class BaseOptions {

    constructor({
        options,
        instance,
        parent
    }) {

        this.instance = instance;
        this.options = options;
        this.parent = parent;
        this.children = [];
    }


    initMixin() {
        let { options,instance } = this;
        let { mixins = []} = options
        options.mixins = mixins.map(mix => {
            let Ctor = this.constructor
            let NativeOptions = new Ctor({
                options:mix,
                parent:this
            })
            this.children.push(NativeOptions);
            NativeOptions.init();
            let config = NativeOptions.getConfig()
            return isWeixin() ? Behavior(config) : config;
        });


        this.mergeObservers(); // 合并原生的

        // 合并全局的mixins
        if (instance) {
            this.mergeGlobalMixins();
          
        }
     }


    // 因为历史原因，全区的mixins不能动了 只做转key名称
    mergeGlobalMixins() {
        if (!this.instance) return ;
        let { options ,instance } = this;
        let Ctor = instance.constructor;
        let { $options } = Ctor;
        let globalMixins = $options.mixins || [];
        globalMixins = globalMixins.concat([emitConfig])
        globalMixins = globalMixins.map((config) => {
            let nativeConfig = {};
            let map;
            if (isWeixin()) map = weappComponentMap;else if (isAlipay()) map = aliComponentMap;
            mapKeys(config, nativeConfig, map);
            return isWeixin() ?  Behavior(nativeConfig) : nativeConfig;
        })
        options.mixins = globalMixins.concat(options.mixins);
    }


    mergeObservers() {
        let { options  } = this; 
        if (options.behaviors && Array.isArray(options.behaviors)) {
            options.mixins = options.mixins.concat(options.behaviors)
        }
    }

    getConfig () {
        let sqbOptions = {};
        let options = this.options;
        let map;
        if (isWeixin()) map = weappComponentMap;
        else if (isAlipay()) map = aliComponentMap;
        mapKeys(options, sqbOptions, map);
        return sqbOptions;
      };


      findRootOptions() {

        if (this.instance) return this;
        else return this.parent.findRootOptions();
      }


      processBeforeRun(vm,runner) {
        if (vm.hasInitedBeforeRun) return ;
        vm.hasInitedBeforeRun = true
        let root = this.findRootOptions();
        let { children } = root;
        runner.call(root,vm)
        children.forEach((child) => {
            runner.call(child,vm);
        })
      }
}
