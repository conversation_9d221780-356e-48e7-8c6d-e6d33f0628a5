import {
  checkPropValid,
  isObject,
  isFunction,
  deepEqual,
  fill,
  proxyDataWidthProps,
  initMixins,
  initTd,
  bindMethods,
  mergeGlobalProps,
  initQuickState,
} from '../../helper/util';

import { createAlipayBasicMixins } from '../../mixins/index';

import {
  MyOptions
} from "./options"

let AlipayPrototype = {
  __init: function() {

    let { options } = this;
    if (options.V2) {
      let op = new MyOptions({
        options:this.options,
        instance:this,
        parent:null
      })
  
      op.init();
      this.options = op.options;
  

    }else {
      this.initMethods();
      this.initProps();
      this.initMixin();
      this.initObservers();
      this.initCreated();
    }

  },

  initMethods() {
    let options = this.options;
    let methods = options.methods || {};
    this._initMethods(methods,Object.keys(methods))
  },

  _initMethods(methods,keys) {
    let { options } = this;
    for (let i = 0; i < keys.length;i++) {
      let key = keys[i]
      if (!isFunction(methods[key])) continue ;
      fill(methods, key, (origin) => {
        return function(...args) {
          // 支付宝每次setData后，data就是全新的对象
          // 在函数这里代理下
          proxyDataWidthProps.call(this, options);
          return origin.call(this, ...args);
        };
      });
    }

  },

  initMixinsMethods(comp) {

    if (comp.__hasInitMixinsMethods) return ;
    let { _copmMixins : mixins  } = this

    let keys = []

    mixins.forEach((mix) => {
      if (mix && mix.methods && isObject(mix.methods)) {
        keys = keys.concat(Object.keys(mix.methods));
      }
    })

    this._initMethods(comp,keys)
    

    comp.__hasInitMixinsMethods = true;

  },

  initProps: function() {
    mergeGlobalProps(this);
    let { props = {} } = this.options;

    for (var key in props) {
      if (typeof props[key] === 'function') continue;
      checkPropValid(props[key], key);
      if (props[key] && props[key]['type']) {
        props[key] = props[key]['value'] || props[key]['type']();
      }
    }
  },

  initMixin: function() {
    this._copmMixins = this.options.mixins ? [...this.options.mixins] : []
    initMixins(this, createAlipayBasicMixins);
  },

  initObservers() {
    let options = this.options;
    if (!isObject(options)) return;
    if (!options.observers) return;
    if (!isObject(options.observers)) return;

    let deps = {};
    var observers = options.observers;
    for (var key in observers) {
      if (isFunction(observers[key])) {
        deps[key] = observers[key];
      }
    }
    this.deps = deps;
    let self = this;

    fill(options, 'didUpdate', (origin) => {
      return function(prevProps, prevData) {
        proxyDataWidthProps.call(this, options);
        self.initMixinsMethods(this)
        initQuickState(this, options);
        var depKeys = Object.keys(deps);
        if (depKeys.length) {
          depKeys.forEach((key) => {
            // 兼容A.B写法
            let keys = key.split(',').map((k) => k.trim());
            let params = keys.map((k) => {
              return this.props[k];
            });
            keys.forEach((k) => {
              if (!deepEqual(prevProps[k], this.props[k])) {
                deps[key] && deps[key].apply(this, params);
              }
            });
          });
        }
        if (origin) return origin.call(this, prevProps, prevData);
      };
    });
  },

  immediateObservers(vm) {
    let { deps = {} } = this;
    var depKeys = Object.keys(deps);
    if (depKeys.length) {
      depKeys.forEach((key) => {
        let keys = key.split(',').map((k) => k.trim());

        let params = keys.map((k) => {
          return vm.props[k];
        });

        deps[key].apply(vm, params);
      });
    }
  },

  initCreated() {
    let options = this.options;
    let self = this;
    fill(options, 'created', (origin) => {
      return function(...args) {
        proxyDataWidthProps.call(this, options);
        bindMethods(options, this);
        initQuickState(this, options);
        initTd(options, this);
        self.initMixinsMethods(this)

        // 在微信端 observers 如果组件属性值和父传递数相等，则不会执行，否则执行
        // 在支付宝端，只有父组件setdata导致数据改变才会触发子组件相关数据回调
        // 为了保持两端一直，支付宝在created的时候，执行一遍observers的函数
        self.immediateObservers(this);

        if (origin) return origin.call(this, ...args);
      };
    });
  },
};

export default AlipayPrototype;
