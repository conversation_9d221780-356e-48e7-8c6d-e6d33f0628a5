import { fill, getUid } from '../../helper/util';
export const aliPagePrototype = {
  __init() {
    this.initCreated();
    return;
  },

  /**
   *
   * 微信格式 {
   *  title:"",
   * path:"",
   * imageUrl:""
   * }
   *
   * 支付宝格式 {
   *  title:"",
   * path:"",
   * desc:"",
   * imageUrl:""
   * }
   */
  // wrapShareMessage(vm){
  //   let { onShareAppMessage } =  vm;
  //   if (onShareAppMessage) {
  //     fill(vm,'onShareAppMessage',(origin) => {
  //       return function (...args) {
  //        let res = origin.call(this, ...args);
  //         res.
  //        return res;
  //       }
  //     })
  //   }
  // },

  initCreated() {
    let { options } = this;
    fill(options, 'created', (origin) => {
      return function(...args) {
        this.__sqbPageId = getUid();
        if (origin) origin.call(this, ...args);
      };
    });
  },
};
