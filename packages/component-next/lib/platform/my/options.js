import { BaseOptions } from "../options.js";

import {
    checkPropValid,
    isObject,
    isFunction,
    deepEqual,
    fill,
    proxyDataWidthProps,
    initTd,
    bindMethods,
    mergeGlobalProps,
    initQuickState,
  } from '../../helper/util';

export class MyOptions extends BaseOptions{



    init() {

        this.initMethods();
        this.initProps();
        this.initMixin();
        this.initObservers();
        this.initCreated();

    }


    initProps() {
        let { instance,options } = this;
        if (instance)  mergeGlobalProps(instance,options);

        let { props = {} } = this.options;
    
        for (var key in props) {
          if (typeof props[key] === 'function') continue;
          checkPropValid(props[key], key);
          if (props[key] && props[key]['type']) {
            props[key] = props[key]['value'] || props[key]['type']();
          }
        }
      }

    initMethods() {
        let options = this.options;
        let methods = options.methods || {};
        for (let key in methods) {
          fill(methods, key, (origin) => {
            return function(...args) {
              // 支付宝每次setData后，data就是全新的对象
              // 在函数这里代理下
              proxyDataWidthProps.call(this, options);
              return origin.call(this, ...args);
            };
          });
        }
    }


    

    initObservers() {
        let options = this.options;
        if (!isObject(options)) return;
        if (!options.observers) return;
        if (!isObject(options.observers)) return;
    
        let deps = {};
        var observers = options.observers;
        for (var key in observers) {
          if (isFunction(observers[key])) {
            deps[key] = observers[key];
          }
        }
        this.deps = deps;
        let self = this;
    
        fill(options, 'didUpdate', (origin) => {
          return function(prevProps, prevData) {
            proxyDataWidthProps.call(this, options);
            initQuickState(this, options);
            var depKeys = Object.keys(deps);
            if (depKeys.length) {
              depKeys.forEach((key) => {
                // 兼容A.B写法
                let keys = key.split(',').map((k) => k.trim());
                let params = keys.map((k) => {
                  return this.props[k];
                });
                keys.forEach((k) => {
                  if (!deepEqual(prevProps[k], this.props[k])) {
                    deps[key] && deps[key].apply(this, params);
                  }
                });
              });
            }
            if (origin) return origin.call(this, prevProps, prevData);
          };
        });
      }


      immediateObservers(vm) {
        let { deps = {} } = this;
        var depKeys = Object.keys(deps);
        if (depKeys.length) {
          depKeys.forEach((key) => {
            let keys = key.split(',').map((k) => k.trim());
    
            let params = keys.map((k) => {
              return vm.props[k];
            });
    
            deps[key].apply(vm, params);
          });
        }
      }
    
      initCreated() {
        let options = this.options;
        let self = this;
        fill(options, 'created', (origin) => {
          return function(...args) {
            self.processBeforeRun(this,self.processBeforeRunner);
            if (origin) return origin.call(this, ...args);
          };
        });
      }


      processBeforeRunner(vm) {
        let { options } = this;
        proxyDataWidthProps.call(vm, options);
        bindMethods(options, vm);
        initQuickState(vm, options);
        initTd(options, vm);
        // 在微信端 observers 如果组件属性值和父传递数相等，则不会执行，否则执行
        // 在支付宝端，只有父组件setdata导致数据改变才会触发子组件相关数据回调
        // 为了保持两端一直，支付宝在created的时候，执行一遍observers的函数
        this.immediateObservers(vm);
    }

}