import Instance from './instance/index';
import EPage from './page/index';
import { useMixIns } from './mixins/index';
import { isAlipay } from '@wosai/emenu-mini-utils';

function sqbComponent(options) {
  let instance = new Instance(options);
  let config = instance.getConfig();
  if (config.relations && isAlipay()) {
    config.options = {
      ...config.options,
      relations: true,
    };
  }

  return Component(config);
}

function sqbPage(options) {
  let instance = new EPage(options);
  let config = instance.getConfig();
  return Page(config);
}

export { Instance, sqbPage, useMixIns };
export default sqbComponent;
