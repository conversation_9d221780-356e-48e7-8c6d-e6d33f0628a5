import { isWeixin, isAlipay, mapPageKeys } from '../helper/util';

import { aliPageMap, weappPageMap } from '../helper/const';

import { aliPagePrototype } from '../platform/my/page';
import { weappPagePrototype } from '../platform/wx/page';

export default function EPage(options) {
  this.options = options;
  this.__init();
}

if (isWeixin()) {
  EPage.prototype = Object.create(weappPagePrototype);
} else if (isAlipay()) {
  EPage.prototype = Object.create(aliPagePrototype);
} else {
  EPage.prototype = Object.create(weappPagePrototype);
}

EPage.prototype.constructor = EPage;

EPage.prototype.getConfig = function() {
  let sqbOptions = {};
  let options = this.options;

  let map;
  if (isWeixin()) map = weappPageMap;
  else if (isAlipay()) map = aliPageMap;
  mapPageKeys(options, sqbOptions, map);
  console.log('###sqbOptions is ', sqbOptions);
  return sqbOptions;
};
