{"name": "@wosai/emenu-mini-component-next", "version": "6.1.1", "description": "mini core component-next", "maintainers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "keywords": ["emenu", "core", "component"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://git.wosai-inc.com:MK/emenu-mini-core", "license": "ISC", "main": "lib/index.js", "types": "dist/index.d.ts", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib", "dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "*********************:MK/emenu-mini-core.git"}, "scripts": {"test": "jest", "build": "rollup -c ../../rollup.config.js", "release": "npm publish"}, "gitHead": "f24db26f676901bb8625ccf77b945f1d31f698d1", "devDependencies": {}}