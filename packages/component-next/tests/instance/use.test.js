import use from '../../lib/instance/use';

test('#use - with plugin', () => {
  const Instance = {};
  use(Instance);
  const plugin = {
      install: jest.fn()
  };

  Instance.use(plugin, 2, 3); // ?

  expect(Instance).toEqual({
    _installedPlugins: [{ install: plugin.install }],
    use: expect.any(Function),
  });

    expect(plugin.install).toBeCalledTimes(1)
});

test('#use - with plugin object', () => {
  const Instance = {};
  use(Instance);
  const plugin = undefined;

  Instance.use(plugin); // ?

  expect(Instance).toEqual({
    use: expect.any(Function),
  });
});

test('#use - with plugin function', () => {
  const Instance = {};
  use(Instance);
  const plugin = jest.fn();

  Instance.use(plugin); // ?

  expect(Instance).toEqual({
    use: expect.any(Function),
    _installedPlugins: [plugin],
  });

  expect(plugin).toBeCalledTimes(1);
});
