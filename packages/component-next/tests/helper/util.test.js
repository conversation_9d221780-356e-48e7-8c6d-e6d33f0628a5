import { getTag, isArray, isObject } from '../../lib/helper/util';

describe('@util', () => {
  it('#getTag', () => {
    expect(getTag()).toBe('[object Undefined]');
    expect(getTag('1')).toBe('[object String]');
    expect(getTag(1)).toBe('[object Number]');
    expect(getTag(null)).toBe('[object Null]');
    expect(getTag({})).toBe('[object Object]');
    expect(getTag([])).toBe('[object Array]');
    expect(getTag(undefined)).toBe('[object Undefined]');
  });
  it('#isArray', () => {
    expect(isArray(1)).toBe(false);
    expect(isArray([])).toBe(true);
    expect(isArray({})).toBe(false);
  });

  it('#isObject', () => {
    expect(isObject(1)).toBe(false);
    expect(isObject({})).toBe(true);
    expect(isObject([])).toBe(false);
  });
});
