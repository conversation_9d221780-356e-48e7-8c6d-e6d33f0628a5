import SqbComponent, { Instance } from '../lib/index';
// const SqbComponent = require('../lib/index');

function WrapComponent(options) {
  return SqbComponent(options);
}

describe('#Component', function() {
  it('should work', function() {
    Instance.useMixin({
      created() {},
    });
    expect(SqbComponent).toBeDefined();

    SqbComponent({});
  });

  describe("#WrapComponent", function() {
    it("should ", function() {
      Instance.useMixin({
        created() {
          console.log('xxxxx');
        }
      })
      const component = WrapComponent({ a: 1 });
      // component.Instance; // ?
      // component.$options // ?
      Instance.$options // ?
       expect(component).toEqual(1);
    });
  });
});
