{"name": "@wosai/emenu-mini-base-components", "description": "", "version": "1.0.0", "scripts": {"build": "npm run clean && npm run build:wx && npm run build:my && npm run ts", "build:wx": "node scripts/build.js", "build:my": "node scripts/build.js --env my", "dev": "npm run clean && node scripts/build.js -w && npm run ts", "ts": "rollup -c ./rollup.config.js", "clean": "rm -rf dist types"}, "files": ["dist", "types"], "entry": "lib/index.ts", "main": "dist/index.js", "types": "types/base-components/lib/index.d.ts", "dependencies": {"@wosai/pofe-runtime": "1.9.0", "@wosai/pofe-native-api": "1.9.0", "@wosai/emenu-mini-dayjs": "6.0.25", "@wosai/emenu-mini-lodash": "6.0.25"}, "devDependencies": {"@types/node": "^18.7.14", "glob": "^7.1.7", "@wosai/pofe-compiler": "1.9.0", "@babel/plugin-transform-arrow-functions": "^7.18.6"}}