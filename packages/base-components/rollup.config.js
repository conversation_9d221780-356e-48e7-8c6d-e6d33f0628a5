import path from 'path';
import ts from 'rollup-plugin-typescript2';

const json = require('rollup-plugin-json');

const tsPlugin = ts({
  tsconfig: path.resolve(__dirname, '../../tsconfig.json'),
  tsconfigOverride: {
    compilerOptions: {
      target: 'es2015',
      declaration: true,
      emitDeclarationOnly: true,
      declarationMap: false,
    },
    include: [`${path.resolve(__dirname, './lib/**/*')}`],
    exclude: ['**/__tests__', 'test-dts'],
  },
});

export default {
  input: path.resolve(__dirname, './lib/index.ts'),

  output: {
    file: path.resolve(__dirname, './types/index.js'),
    format: `es`,
  },

  plugins: [json(), tsPlugin],
};
