const glob = require('glob');
const Path = require('path');
const relative = require('relative');
let env = process.env.PLATFORM_ENV;

function getEntry(rootSrc, rel, opts = {}) {
  var map = {};
  // glob.sync(rootSrc + rel, opts).forEach((file) => {
  glob.sync(`${rootSrc}${rel}`, opts).forEach((file) => {
    var key = relative(rootSrc, file);
    map[key] = file;
  });
  return map;
}

const srcPath = Path.resolve(__dirname, '../lib');
let opts = {};
let component = getEntry(srcPath, '/**/{*.ts,*.wxs,style/*.less}', opts);
let entry = Object.assign({}, component);

module.exports = {
  entry,
  externals: (path) => {
    let reg = /^@wosai\//;
    if (reg.test(path)) return true;
    return false;
  },
  dist: Path.resolve(__dirname, `../dist/${env}`),
};
