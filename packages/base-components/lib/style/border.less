@import './mixins';

.bd-t,
.bd-b,
.bd-l,
.bd-r {
  position: relative;
}

.border,
.border-x,
.border-y {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  content: '';
  background: #efefef;
}

.border-x,
.bd-t:before,
.bd-b:after {
  position: absolute;
  content: '';
  background: #efefef;
  height: 1px;
  width: 100%;
  //transform: scaleY(0.75);
}

.bd-b:after {
  top: auto;
  bottom: 0;
  left: 0;
}
.bd-t::before {
  top: 0;
  bottom: auto;
  left: 0;
}

.border-y,
.bd-l:before,
.bd-r:after {
  height: 100%;
  width: 1px;
  transform: scaleX(0.75);
}

.bd-r:after {
  left: auto;
  right: 0;
}

.bordered {
  position: relative;
  .bordered-mixin();
}
