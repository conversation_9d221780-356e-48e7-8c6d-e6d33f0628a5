.flex-row,
.flex-column,
.flex-center,
.flex-center-x,
.flex-center-y {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-center-x {
  justify-content: center;
}

.flex-center-y {
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-right {
  justify-content: flex-end;
}

.flex-end {
  align-items: flex-end;
}
.flex-start {
  align-items: flex-start;
}
.flex-item {
  flex: 1;
}

.flex-row > .flex-item {
  width: 1px; /* no */
}

.flex-shrink {
  flex-shrink: 0;
}
.flex-baseline {
  align-items: baseline;
}
.flex-baseline {
  align-items: baseline;
}
