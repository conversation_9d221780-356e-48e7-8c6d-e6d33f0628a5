/* prettier-ignore */
@import './const';
@import './font';

.common-hover {
  opacity: 0.7;

  &.common-hover--disabled {
    opacity: 1;
  }
}
g .color-white {
  color: #fff;
}
// ======================== 背景颜色 ===================================
.bg-f5 {
  background-color: #f5f5f5;
}
.bg-f6 {
  background-color: #f6f6f6;
}
.bg-ccc {
  background-color: #cccccc;
}
.bg-fd6d05 {
  background-color: #fd6d05;
}
.bg-white {
  background-color: #fff;
}
.bg-black {
  background-color: #000;
}
.bg-lightblack {
  background-color: #333;
}
.bg-gray {
  background-color: #666;
}
.bg-lightgray {
  background-color: #999;
}
.bg-orange {
  background-color: #ff6a16;
}
.bg-none {
  background: transparent;
}
.bg-img {
  background-repeat: no-repeat;
  background-size: contain;
  // background-size: 100% auto;
  background-position: center;
}
// ======================== MARGIN ===================================
.mg-0 {
  margin: 0;
}
.mt-0 {
  margin-top: 0;
}
.mr-0 {
  margin-right: 0;
}
.mb-0 {
  margin-bottom: 0;
}
.ml-0 {
  margin-left: 0;
}
.mr-2 {
  margin-right: 2px;
}
.ml-2 {
  margin-left: 2px;
}
.mr-4 {
  margin-right: 4px;
}
.ml-4 {
  margin-left: 4px;
}
.mt-4 {
  margin-top: 4px;
}
.mg-6 {
  margin: 6px;
}
.mt-6 {
  margin-top: 6px;
}
.mr-6 {
  margin-right: 6px;
}
.mb-6 {
  margin-bottom: 6px;
}
.ml-6 {
  margin-left: 6px;
}
.mg-8 {
  margin: 8px;
}
.mt-8 {
  margin-top: 8px;
}
.mr-8 {
  margin-right: 8px;
}
.mb-8 {
  margin-bottom: 8px;
}
.ml-8 {
  margin-left: 8px;
}
.mg-10 {
  margin: 10px;
}
.mt-10 {
  margin-top: 10px;
}
.mr-10 {
  margin-right: 10px;
}
.mb-10 {
  margin-bottom: 10px;
}
.ml-10 {
  margin-left: 10px;
}
.mg-12 {
  margin: 12px;
}
.mt-12 {
  margin-top: 12px;
}
.mr-12 {
  margin-right: 12px;
}
.mb-12 {
  margin-bottom: 12px;
}
.ml-12 {
  margin-left: 12px;
}
.mg-16 {
  margin: 16px;
}
.mt-16 {
  margin-top: 16px;
}
.mr-16 {
  margin-right: 16px;
}
.mb-16 {
  margin-bottom: 16px;
}
.ml-16 {
  margin-left: 16px;
}
.mg-18 {
  margin: 18px;
}
.ml-18 {
  margin-left: 18px;
}
.mt-18 {
  margin-top: 18px;
}
.mr-18 {
  margin-right: 18px;
}
.mb-18 {
  margin-bottom: 18px;
}
.mg-20 {
  margin: 20px;
}
.mt-20 {
  margin-top: 20px;
}
.mr-20 {
  margin-right: 20px;
}
.mb-20 {
  margin-bottom: 20px;
}
.ml-20 {
  margin-left: 20px;
}
.mx-20 {
  margin-left: 20px;
  margin-right: 20px;
}
.mg-24 {
  margin: 24px;
}
.mt-24 {
  margin-top: 24px;
}
.mb-24 {
  margin-bottom: 24px;
}
.ml-24 {
  margin-left: 24px;
}
.mr-24 {
  margin-right: 24px;
}
.mg-30 {
  margin: 30px;
}
.mb-26 {
  margin-bottom: 26px;
}
.mt-26 {
  margin-top: 26px;
}
.mt-30 {
  margin-top: 30px;
}
.mt-34 {
  margin-top: 34px;
}
.mr-30 {
  margin-right: 30px;
}
.mr-32 {
  margin-right: 32px;
}
.mb-30 {
  margin-bottom: 30px;
}
.ml-30 {
  margin-left: 30px;
}
.mt-32 {
  margin-top: 32px;
}
.mt-36 {
  margin-top: 36px;
}
.mg-40 {
  margin: 40px;
}
.mt-40 {
  margin-top: 40px;
}
.mr-40 {
  margin-right: 40px;
}
.mb-40 {
  margin-bottom: 40px;
}
.ml-40 {
  margin-left: 40px;
}
.mt-46 {
  margin-top: 46px;
}
// ======================== PADDING ===================================
.pd-6 {
  padding: 6px;
}
.pr-6 {
  padding-right: 6px;
}
.pl-6 {
  padding-left: 6px;
}
.pd-0 {
  padding: 0;
}
.pd-8 {
  padding: 8px;
}
.pr-8 {
  padding-right: 8px;
}
.pl-8 {
  padding-left: 8px;
}
.pr-0 {
  padding-right: 0;
}
.pl-0 {
  padding-left: 0;
}
.pt-16 {
  padding-top: 16px;
}
.pt-20 {
  padding-top: 20px;
}
.pb-26 {
  padding-bottom: 26px;
}
.px-20 {
  padding-left: 20px;
  padding-right: 20px;
}
.py-26 {
  padding-top: 26px;
  padding-bottom: 26px;
}
.pd-10 {
  padding: 10px;
}
.pr-10 {
  padding-right: 10px;
}
.pl-10 {
  padding-left: 10px;
}
.pd-12 {
  padding: 12px;
}
.pr-12 {
  padding-right: 12px;
}
.pl-12 {
  padding-left: 12px;
}
.pd-16 {
  padding: 16px;
}
.pr-16 {
  padding-right: 16px;
}
.pl-16 {
  padding-left: 16px;
}
.pd-18 {
  padding: 18px;
}
.pr-18 {
  padding-right: 18px;
}
.pl-18 {
  padding-left: 18px;
}
.pd-20 {
  padding: 20px;
}
.pr-20 {
  padding-right: 20px;
}
.pl-20 {
  padding-left: 20px;
}
.pd-30 {
  padding: 30px;
}
.pt-30 {
  padding-top: 30px;
}
.pr-30 {
  padding-right: 30px;
}
.pl-30 {
  padding-left: 30px;
}
.pb-30 {
  padding-bottom: 30px;
}
.pd-40 {
  padding: 40px;
}
.pr-40 {
  padding-right: 40px;
}
.pl-40 {
  padding-left: 40px;
}
// ======================== 位置 ===================================
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.disabled {
  pointer-events: none;
  opacity: 0.5;
}
.abs-full {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.sticky {
  position: sticky;
  position: -webkit-sticky;
  //top: 0;
}
.position-xy {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

// ======================== 字体 FONT-FAMILY ===================================
.font-medium {
  font-family: PingFangSC-Medium, PingFang SC;
}
.font-regular {
  font-family: PingFangSC-Regular, PingFang SC;
}
.lato {
  font-family: 'Lato', PingFang SC, Helvetica Neue, Arial, sans-serif;
}
.helvetica {
  font-family: 'Helvetica', PingFang SC, Helvetica Neue, Arial, sans-serif;
}
.helvetica-light {
  font-family: 'Helvetica Light', PingFang SC, Helvetica Neue, Arial, sans-serif;
}
.helvetica-bold {
  font-weight: bold;
  font-family: 'Helvetica Bold', PingFang SC, Helvetica Neue, Arial, sans-serif;
}
// ======================== 字体大小 FONT-SIZE ===================================
.text-20 {
  font-size: 20px;
}
.text-22 {
  font-size: 22px;
}
.text-24 {
  font-size: 24px;
}
.text-26 {
  font-size: 26px;
}
.text-28 {
  font-size: 28px;
}
.text-30 {
  font-size: 30px;
}
.text-32 {
  font-size: 32px;
}
.text-34 {
  font-size: 34px;
}
.text-36 {
  font-size: 34px;
}
.text-42 {
  font-size: 42px;
}
.text-44 {
  font-size: 44px;
}

/* prettier-ignore */
.font-20 {
  font-size: 20PX;
}

/* prettier-ignore */
.font-15 {
  font-size: 15PX;
}

/* prettier-ignore */
.font-13 {
  font-size: 13PX;
}

/* prettier-ignore */
.font-10 {
  font-size: 10PX;
}
// ======================== 字体颜色 COLOR ===================================
.color-white {
  color: #fff;
}
.color-999 {
  color: #999999;
}
.color-666 {
  color: #666666;
}
.color-b6 {
  color: #b6b6b6;
}
.color-4a {
  color: #4a4a4a;
}
.color-0d {
  color: #0d0d0d;
}
.color-fd6d05 {
  color: #fd6d05;
}
.text-black {
  color: black;
}
.text-red {
  color: #f54a27;
}
.text-brown {
  color: #513522;
}
.text-grey-ccc {
  color: #ccc;
}
.color-969799 {
  color: #969799;
}
.color-black {
  color: #000;
}
.color-lightblack {
  color: #333;
}
.color-lightgray {
  color: #999;
}
.color-gray {
  color: #666;
}
.color-orange {
  color: #f24839;
}
// ======================== 字体位置 TEXT-ALIGN ===================================
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
// ======================== 行高度 LINE-HEIGHT ===================================
.lh-1 {
  line-height: 1;
}
.lh-120 {
  line-height: 120%;
}
.line-height-1 {
  line-height: 1;
}
// ======================== 字体粗细 FONT-WEIGHT ===================================
.fw-500 {
  font-weight: 500;
}
.fw-400 {
  font-weight: 400;
}
.bold {
  font-weight: bolder;
}
// ======================== 高度 宽度 WIDTH HEIGHT ===================================
.h-full-100 {
  height: 100%;
}
.w-full-100 {
  width: 100%;
}
.w-full {
  width: 100vw;
}
.w-f100 {
  width: 100%;
}
.h-screen {
  height: 100vh;
}
.h-full {
  height: 100%;
}
.price {
  &:before {
    content: '￥';
    font-size: 75%;
  }
}
// ======================== 分割线 HR===================================
.hr-ef {
  height: 1px;
  background: #efefef;
  widht: 100%;
}
// ======================== 圆角 BORDER-RADIUS ===================================
.rounded-10 {
  border-radius: 12px;
}
.rounded-12 {
  border-radius: 12px;
}
.rounded-45 {
  border-radius: 45px;
}
// ======================== OVERFLOW ===================================
.overflow-y-auto {
  overflow-y: auto;
}
.scroll-y {
  overflow-y: auto;
}
.scroll-x {
  overflow-x: auto;
}
// ======================== Z-INDEX ===================================
.z-index-100 {
  z-index: 100;
}
// ======================== 其他 ===================================
.events-none {
  pointer-events: none;
}
//按钮热区放大
.hotspot {
  cursor: pointer;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 999px;
  background: transparent;
  transform-origin: 50% 50%;
  transform: translate(-50%, -50%) scale(1.5);
}
.c-nowrap {
  white-space: nowrap;
}
.unvisible {
  opacity: 0;
}
.mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.ellipsis,
.ellipsis-2,
.ellipsis-3 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  &.flex-item {
    display: block;
    width: 1px;
  }

  &.ellipsis-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    white-space: normal;
    word-break: break-all;
    -webkit-line-clamp: 2;
  }

  &.ellipsis-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    white-space: normal;
    word-break: break-all;
    -webkit-line-clamp: 3;
  }
}
.hidden-holder {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0;
  border: none;
  border-radius: 0;
  background: transparent !important;
  color: transparent !important;
  z-index: 99;

  &:after,
  &:before {
    display: none !important;
    border: none !important;
  }
}
.line-through {
  text-decoration: line-through;
}
.text-underline {
  text-decoration: underline;
}
.line-clamp-2 {
  display: block;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
  // white-space: nowrap;
  // line-clamp: 2;
}
.must-order-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff6a16;
  border: 1px solid #ff6a16;
  border-radius: 1000px;
  padding: 0 10px;
  font-size: 20px;
  margin-right: 10px;
  flex-shrink: 0;
  height: 30px;
  transform: rotate(360deg);
  box-sizing: border-box;
}
.campus-tag {
  color: rgba(112, 211, 242, 1);
  border: 1px solid rgba(112, 211, 242, 1);
  border-radius: 8px;
  padding: 4px 10px;
  font-size: 20px;
  margin-right: 10px;
  flex-shrink: 0;
  height: 20px;
  line-height: 20px;
}
