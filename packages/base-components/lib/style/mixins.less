@default-borders: top, right, bottom, left;

.font-regular {
  font-family: PingFangSC-Regular, 'PingFang SC';
  font-weight: 400;
}

.font-medium {
  font-family: PingFangSC-Medium, 'PingFang SC';
  font-weight: 500;
}

.font-semibold {
  font-family: PingFangSC-Semibold, 'PingFang SC';
  font-weight: 600;
}

.position-x {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.position-y {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.position-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.c-box-wrap {
  background: #fff;
  border-radius: 12px;
}

.bold {
  font-weight: bolder;
}

.bordered-mixin(@color: #dddddd, @radius: 0, @borders: @default-borders, @width: 1, @times: 3 ) {
  position: relative;
  &::before {
    position: absolute;
    content: '';
    width: calc(@times * 100%);
    height: calc(@times * 100%);
    top: -100%;
    left: -100%;
    each(@borders, {
          border-@{value}: unit(@times, px) solid @color;
        });
    transition: none;
    transform: scale(calc(1 / @times));
    border-radius: unit(@radius * @times, px);
    box-sizing: border-box;
  }
}
