/* eslint-disable */
// noinspection ES6ConvertVarToLetConst

var bem = require('./bem.wxs').bem;

var UNIT_IGNORE = ['lineHeight', 'fontWeight', 'opacity', 'zIndex'];

var REGEXP_OBJECT = getRegExp('{([^{}]*)}', 'g');
//var REGEXP_ARRAY = getRegExp('\\[([^\[\]]*)\\]', 'g');
/* ----- 测试用例专用 ----- */
// var REGEXP_OBJECT = /\{([^{}]*)\}/g;
// var REGEXP_ARRAY = /\[([^[]]*)\]/g;

function addUnit(value) {
  if (value == null) {
    return undefined;
  }
  return getRegExp('^d+(.d+)?$').test('' + value) ? value + 'px' : value;
}

/**
 * 条件判断工具方法，当满足条件时返回指定内容
 * @param condition
 * @param message
 * @returns {*}
 */
function when(condition, message) {
  if (condition) {
    return message;
  }
}

/**
 * 将px单位转换为rpx
 * @example
 *  pxTransform(40) // 80rpx
 *  pxTransform('40px') // 80rpx
 * @param {number|string} value
 * @param {string} [unit], 单位，默认rpx
 * @returns {string}
 */
function pxTransform(value, unit) {
  unit = unit || 'rpx';
  if (typeof value === 'string' && getRegExp('^s*[0-9.]+s*px$').test(value)) {
    value = parseInt(value, 10);
  }
  return value * 2 + unit;
}

/**
 * 获取对象keys
 * * @example
 * keys({a: 1, b: 2, c: {d: 3, e: 4}})     // ['a', 'b', 'c']
 * @param {object} object
 * @return {array}
 */

function keys(object) {
  var REGEXP = getRegExp('{|}|"', 'g');
  var str = JSON.stringify(object);
  if (str) {
    return str
      .replace(REGEXP, '')
      .split(',')
      .map(function (item) {
        return item.split(':')[0];
      });
  }
}

/**
 *function keys(object) {
 * var string = JSON.stringify(object).slice(1, -1);
 *  string = _clean(string, REGEXP_OBJECT);
 *  string = _clean(string, REGEXP_ARRAY);
 *  return string.split(",").map(function (v) {
 *    return v.split(":")[0].slice(1, -1);
 *  });
 *
 *  function _clean(string, rule) {
 *   string = string.replace(rule, 1);
 *   if (rule.test(string)) {
 *     string = _clean(string, rule);
 *    }
 *    return string;
 *  }
 * }
 */

/**
 * 判断一个值是否是null或者undefined
 * @param {*} value
 * @return {boolean}
 */
function isNil(value) {
  return value === null || value === undefined;
}

/**
 * 判断对象是否为空
 * @param {*} value
 * @return {boolean}
 */
function isEmpty(value) {
  if (isArray(value) || isString(value)) {
    return !value.length;
  } else if (isObject(value)) {
    return !keys(value).length;
  }
  return true;
}

/**
 * 判断一个值是否为数字
 * @param {*} value
 * @return {boolean}
 */
function isNumber(value) {
  return typeof value === 'number';
}

/**
 * 判断一个值是否为字符串
 * @param {*} value
 * @return {boolean}
 */
function isString(value) {
  return typeof value === 'string';
}

/**
 * 判断一个值是否为对象
 * @param {*} value
 * @return {boolean}
 */
function isObject(value) {
  return value && value.constructor === 'Object';
}

/**
 * 判断一个值是否为数组
 * @param {*} value
 * @return {boolean}
 */
function isArray(value) {
  return value && value.constructor === 'Array';
}

/**
 * 类数组对象转数组函数
 * @param {arguments|*} value
 * @return {array|undefined}
 */
function toArray(value) {
  var array = [];
  if (!isNil(value.length)) {
    for (var i = 0; i < value.length; i++) {
      array.push(value[i]);
    }
  }
  return array;
}

function applyArguments(args) {
  args = toArray(args);
  return isArray(args[0]) ? args[0] : args;
}

/**
 * 获取数组里的最小值
 * @returns {*}
 */
function min() {
  return applyArguments(arguments).sort()[0];
}

/**
 * 获取数组里的最大值
 * @returns {*}
 */
function max() {
  return applyArguments(arguments).sort(function (a, b) {
    return a < b ? 1 : -1;
  })[0];
}

/**
 * 判断数组中是否包含某一元素
 * @param {array} array
 * @param {*} value
 * @return {boolean}
 */
function includes(array, value) {
  if (!isArray(array)) return false;
  for (var i = 0; i < array.length; i++) {
    if (array[i] === value) {
      return true;
    }
  }
  return false;
}

/**
 * 倍数转换辅助函数，支持指定小数点倍数
 * @example
 * decimal(150, 0.01)     // 1.5
 * decimal(150, 0.01, 2)  // 1.50
 * @param {number|string} value 需要转换的数值
 * @param {number?} multiple
 * @param {number?} digit
 * @return {string|number}
 */
function decimal(value, multiple, digit) {
  value = multiply(value || 0, multiple || 1);
  if (isNumber(digit)) {
    return value.toFixed(digit);
  }
  return value;
}

/**
 * 货币分转元辅助函数
 * @param {number|string} value
 * @param {number?} digit
 * @return {string|number}
 */
function toYuan(value, digit) {
  return decimal(value, 0.01, digit);
}

/**
 * 折扣数值转换函数
 * @param {number|string} value
 * @param {number?} digit
 * @return {string|number}
 */
function toDiscount(value, digit) {
  return decimal(value, 0.1, digit);
}

/**
 * 字符串驼峰转蛇形函数
 * @example
 * snakeCase('helloWorld')        // hello_world
 * snakeCase('helloWorld', '-')   // hello-world
 * @param {string} value 需要转换的字符串
 * @param {string} [separator] 连接符，默认：_
 * @return {string}
 */
function snakeCase(value, separator) {
  separator = separator || '_';
  return (value || '').replace(getRegExp('([A-Z])', 'g'), function () {
    return separator + arguments[1].toLowerCase();
  });
}

/**
 * 对象转换为style字符串
 * @example
 * toStyle({paddingTop: 2, color: '#FFFFFF'})        // padding-top:2px;color:#FFFFFF
 * @param {object} object 需要转换的对象
 * @return {string}
 */
function toStyle(object) {
  if (!object) return;
  var styles = [];
  keys(object).forEach(function (key) {
    var value = object[key];
    if (isNumber(value) || (isString(value) && value)) {
      if (value && isNumber(value) && !includes(UNIT_IGNORE, key)) {
        value = pxTransform(value);
      }
      styles.push(snakeCase(key, '-') + ':' + value);
    }
  });
  return styles.join(';');
}

/**
 * 样式名聚合函数
 * @example
 * classNames('flex-row flex-center', {disabled: false, loading: true});    // flex-row flex-center loading
 * @param {*} arguments
 * @returns {string}
 */
function classNames() {
  var classnames = [];

  var args = [];
  for (var i = 0; i < arguments.length; i++) {
    args.push(arguments[i]);
  }
  args.forEach(function (item) {
    if (isString(item)) {
      item = item.split(' ');
    }
    if (isPlainObject(item)) {
      var available = [];
      keys(item).forEach(function (key) {
        if (item[key]) {
          available.push(snakeCase(key, '-'));
        }
      });
      item = available;
    }
    if (isArray(item)) {
      item.forEach(function (name) {
        if (name && !includes(classnames, name)) {
          classnames.push(name);
        }
      });
    }
  });
  return classnames.join(' ');
}

/**
 * 数组累加器
 * @example
 * reduce([1, 2, 3], function(result, current) {
 *   return result + current
 * }, 0)  // 6
 * reduce(['hello', ' ', 'world', '!'], function(result, current) {
 *   return result + current
 * }, '') // hello world!
 * @param {array} array
 * @param {function} cb
 * @param {*} result
 * @return {*}
 */
function reduce(array, cb, result) {
  array.forEach(function (value) {
    result = cb(result, value);
  });
  return result;
}

/**
 * 安全的乘法函数，支持任意个数的数字相乘
 * @example
 * multiply(1, 2, 3)   // 6
 * multiply(0.99, 100)   // 99
 * @return {number}
 */
function multiply() {
  var numbers = [];
  for (var i = 0; i < arguments.length; i++) {
    numbers.push(arguments[i]);
  }
  var decimal = reduce(
    numbers,
    function (result, current) {
      return ((current + '').split('.')[1] || '').length + result;
    },
    0,
  );
  return (
    reduce(
      numbers,
      function (result, current) {
        return (current + '').replace('.', '') * result;
      },
      1,
    ) / Math.pow(10, decimal)
  );
}

function trackStyle(data) {
  if (!data.animated) {
    return '';
  }

  return [
    'transform: translate3d(' + -100 * data.currentIndex + '%, 0, 0)',
    '-webkit-transition-duration: ' + data.duration + 's',
    'transition-duration: ' + data.duration + 's',
  ].join(';');
}

function assignThemeStyle(source, from, isSnakeCase) {
  var _keys = keys(from);
  for (var i = 0; i < _keys.length; i++) {
    if (isSnakeCase) {
      source[snakeCase(_keys[i], '-')] = from[_keys[i]];
    } else {
      source[_keys[i]] = from[_keys[i]];
    }
  }
  return source;
}

var thresholdOffset = 300;

function getHeaderBarStyle(data) {
  var scrollTop = data.scrollTop;
  var ratio = scrollTop / thresholdOffset > 1 ? 1 : scrollTop / thresholdOffset;
  return ['background:rgba(255,255,255,' + ratio + ')'].join(';');
}

function getHeaderSearchStyle(data) {
  var scrollTop = data.scrollTop;
  var ratio = scrollTop / thresholdOffset > 1 ? 1 : scrollTop / thresholdOffset;
  var maxWidth = 150;
  var width = (ratio * maxWidth).toFixed(0);
  return ['width:' + width + 'px', 'background:rgba(246,246,246,' + ratio + ')'].join(';');
}

function transformColor(origin, process, ratio) {
  var r = origin - (origin - process) * ratio;
  return 'rgb(' + r + ',' + r + ',' + r + ')';
}

function getHeaderSearchIconStyle(data) {
  var scrollTop = data.scrollTop;
  var ratio = scrollTop / thresholdOffset > 1 ? 1 : scrollTop / thresholdOffset;
  var color = transformColor(0, 153, ratio);
  return ['color:' + color].join(';');
}

function arrSize(value) {
  if (isArray(value)) {
    return value.length;
  }
}

function isPlainObject(value) {
  return !!value && 'Object' === value.constructor;
}

function getCouponTagStyle(coupon) {
  if (coupon.discountType === 4) {
    return 'background:#F45446;color:#fff';
  }
  return '';
}

function getGoodItemStyle(item, disabled = false) {
  if (item.out_of_stock || disabled) {
    return ['color:#999999!important'].join(';');
  } else {
    return '';
  }
}

function formatFloat(value, n) {
  value = value || 0;
  var f = Math.round(value * Math.pow(10, n)) / Math.pow(10, n);
  var s = f.toString();
  var rs = s.indexOf('.');
  if (rs < 0) {
    s += '.';
  }
  for (var i = s.length - s.indexOf('.'); i <= n; i++) {
    s += '0';
  }
  return s;
}

function percentToDiscount(percent) {
  percent = percent.toString();
  // 是否百分数
  var isPercent = percent.indexOf('%') > -1;
  if (isPercent) {
    percent = percent.slice(0, -1) * 10;
  } else {
    percent = percent * 10;
  }
  return percent.toFixed(1);
}

function checkAttributeSelected(selectedAttributes, index, subIndex) {
  return selectedAttributes[index] && selectedAttributes[index].indexOf(subIndex) !== -1;
}

function toEllipsis(str, len, isEllipsis) {
  if (!str) return;
  if (typeof isEllipsis === 'undefined') {
    isEllipsis = true;
  }
  if (!len) return str;
  var subStr = str;
  subStr = subStr.substring(0, len);
  if (str.length > len && isEllipsis) {
    subStr += '...';
  }
  return subStr;
}

function transTempToStr(temp, value, reg) {
  reg = reg || getRegExp('(#.*#)');
  return temp && temp.replace(reg, value);
}

function formatSaleCount(value) {
  if (!value) return 0;

  value = +value;

  if (value < 1000) {
    return value;
  } else {
    return (Math.floor(value / 100) / 10).toFixed(1) + 'K';
  }
}

function secondZhe(value) {
  if (value == 50) return '半价';
  return (value / 10).toFixed(1) + '折';
}

/**
 * 数量显示最大数
 * @param num
 * @param max
 * @returns
 */
function showMaxNum(num, max = 99) {
  if (!num) return num;

  var type = typeof num;

  if (type === 'string') num = +num;

  return num > max ? max : num;
}

var __time = null;
var __timeout = function (instance, cb) {
  if (__time) {
    if (getDate().getTime() >= __time) {
      __time = null;
      cb && cb();
    } else {
      instance.requestAnimationFrame(function () {
        __timeout(instance, cb);
      });
    }
  }
};

function debounce(e, cb, time) {
  if (e && e.instance) {
    __time = getDate().getTime() + time;
    __timeout(e.instance, cb);
  }
}

var components = null;
var tapCategoryId = null;

function onScrollHomePage(ev, instance) {
  instance.callMethod('onScrollHomePage', ev);

  var scrollTop = Math.floor(ev.detail.scrollTop);

  components = components || {
    redBag: instance.selectComponent('#red-img'),
    headBar: instance.selectComponent('.header-bar-wrap'),
    headBarBg: instance.selectComponent('.header-bar-bg'),
    backTop: instance.selectComponent('.back-top-box'),
    categoryWrap: instance.selectComponent('.category-wrap'),
    iconHeaderUrls: instance.selectAllComponents('.icon-header'),
  };

  var dataset = ev.target.dataset,
    pages = dataset.simplepages,
    activeCategory = calcActiveCategory(pages, ev),
    activeCategoryId = activeCategory.id;
  // 处理头部icon、背景style相关
  handleHeaderBar(ev, dataset, pages, instance);
  // 处理商品列表页分类名字显示问题
  handleCategoryName(activeCategory, instance, scrollTop, pages);
  // 处理左边导航高亮
  handleCategory(activeCategoryId, instance);
  // instance.callMethod('setCategoryScrollTop', {activeCategoryId: activeCategoryId});
}

// 获取当前分类
function calcActiveCategory(pages, event) {
  if (!pages.length > 0) {
    return {};
  }

  var scrollTop = Math.floor(event.detail.scrollTop);
  var activeCategoryId;
  var offset = 10; // vivo 有些机型会有0.xxPX的误差
  var activeCategoryKey;

  if (scrollTop <= pages[0][1][0]) {
    activeCategoryId = pages[0][0];
    activeCategoryKey = pages[0][2];
  }

  for (var index = 0; index < pages.length; index++) {
    var element = pages[index];

    if (tapCategoryId) {
      if (tapCategoryId === element[0]) {
        activeCategoryId = element[0];
        activeCategoryKey = element[2];
        tapCategoryId = null;
        break;
      }
    } else {
      if (scrollTop + offset >= element[1][0] && scrollTop + offset < element[1][1]) {
        activeCategoryId = element[0];
        activeCategoryKey = element[2];
        break;
      }
    }
  }

  return { id: activeCategoryId, key: activeCategoryKey };
}

function handleCategoryName(_activeCategory, instance, scrollTop, pages) {
  // var activeCategoryId = _activeCategory.id;
  var activeCategoryKey = _activeCategory.key;

  var categoryNameItems = instance.selectAllComponents('.category-name');
  if (activeCategoryKey && categoryNameItems) {
    for (var i = 0; i < categoryNameItems.length; i++) {
      var categoryNameItem = categoryNameItems[i];
      if (categoryNameItem) {
        categoryNameItem.removeClass('show');
        categoryNameItem.removeClass('sticky');
      }
    }

    var categoryName = instance.selectComponent('.' + activeCategoryKey);
    var categoryWrap = instance.selectComponent('.category-wrap');

    // 解决iOS下Stick抖动的问题
    if (categoryName && !categoryName.hasClass('sticky') && scrollTop >= pages[0][1][0] - 10) {
      categoryName.addClass('sticky');
    }

    // 解决iOS下Stick抖动的问题
    if (scrollTop >= pages[0][1][0] - 10) {
      categoryWrap && categoryWrap.addClass('sticky');
    } else {
      categoryWrap && categoryWrap.removeClass('sticky');
    }

    if (categoryName && categoryName.hasClass('same-prev') && !categoryName.hasClass('show')) {
      categoryName.addClass('show');
    }
  }
}

/**
 * 点击分类
 * @param evt
 * @param instance
 */
function onTapCategory(evt, instance) {
  instance.callMethod('onTapCategory', evt);

  var dataset = evt.currentTarget.dataset;
  tapCategoryId = dataset.categoryid;
  handleCategory(dataset.categoryid, instance);
}

function handleHeaderBar(ev, dataset, pages, instance) {
  var newValue = Math.min(dataset.height, ev.detail.scrollTop) / dataset.height;
  var iconStyle = { transform: 'scale(' + newValue + ')' };
  var headerBarPaddingRight = instance;
  var headStyles = {
    height: dataset.height + 'PX',
    paddingTop: dataset.paddingtop + 'PX',
    paddingRight: headerBarPaddingRight + 'PX',
    'background-image': 'none',
  };
  var redBag = components.redBag,
    headBar = components.headBar,
    headBarBg = components.headBarBg,
    backTop = components.backTop,
    categoryWrap = components.categoryWrap,
    themeName = dataset.themename;

  // scrollView 向上滚动时
  var headerBgStyle = dataset.headerbgstyle;
  var navigationBarStyle = dataset.navigationbarstyle;
  if (newValue > 0.6) {
    headStyles = {
      height: dataset.height + 'PX',
      paddingTop: dataset.paddingtop + 'PX',
      paddingRight: headerBarPaddingRight + 'PX',
      'background-image': dataset.headerbgstyle,
    };
    iconStyle = { transform: 'scale(1)' };
    headBarBg && headBarBg.addClass('header-bar-bg--pseudo');
    if (navigationBarStyle) {
      headStyles = assignThemeStyle(headStyles, navigationBarStyle, true);
    }
    // 更新麦当劳头部icons
    if (themeName === 'mcDonald') {
      //   headStyles["background-image"] = "none"
      //   headStyles["background-color"] = "#FFF"
      instance.callMethod('updateHeaderIconsVersion', 'v2');
    }
  } else {
    if (themeName === 'mcDonald') {
      instance.callMethod('updateHeaderIconsVersion', 'v1');
    }
    if (headerBgStyle) {
      headStyles = assignThemeStyle(headStyles, headerBgStyle, true);
    }
    headBarBg && headBarBg.removeClass('header-bar-bg--pseudo');
  }

  redBag && redBag.setStyle(iconStyle);
  headBar && headBar.setStyle(headStyles);

  // 显示回到页面顶部按钮
  if (pages && pages.length > 0) {
    var top = pages[0][1][0];
    var backTopStyle = {
      transform: 'scale(0)',
    };
    if (ev.detail.scrollTop > top) {
      backTopStyle = {
        transform: 'scale(1)',
      };
      categoryWrap.setStyle({ 'overflow-y': 'scroll' });
      // instance.callMethod('setCategoryScrollable', {scrollable: true});
    } else {
      categoryWrap.setStyle({ 'overflow-y': 'hidden' });
      // instance.callMethod('setCategoryScrollable', {scrollable: false});
    }
    backTop && backTop.setStyle(backTopStyle);
  }
  return pages;
}

function handleCategory(activeCategoryId, instance) {
  var categoryItems = instance.selectAllComponents('.category-item');
  if (activeCategoryId && categoryItems) {
    for (var i = 0; i < categoryItems.length; i++) {
      var catItem = categoryItems[i];

      if (catItem) {
        var dataSet = catItem.getDataset();
        var style = dataSet['style'];
        catItem.removeClass('selected');
        catItem.setStyle({ borderLeft: 'none' });
        if (activeCategoryId === dataSet['categoryid'] && !catItem.hasClass('selected')) {
          // addClassName(catItem, ownerInstance)
          catItem.addClass('selected');
          catItem.setStyle(toStyle(style));
          // instance.requestAnimationFrame(function() {
          //     instance.callMethod('setCategoryScrollTop', {activeCategoryId: activeCategoryId});
          // })
        }
      }
    }
  }
}

// function propObserver(newValue, oldValue, ownerInstance, instance) {
//     if(newValue === oldValue) return;
//
//     var categoryItems = ownerInstance.selectAllComponents('.category-wrap .category-item');
//
//     for (var i = 0; i < categoryItems.length; i++) {
//         var catItem = categoryItems[i];
//         if (catItem) {
//             catItem.removeClass('selected');
//         }
//     }
//
//     var currentCatItem = ownerInstance.selectComponent('#menu-cid-'+ newValue);
//
//
//     if(currentCatItem) {
//         currentCatItem.addClass('selected');
//     }
// }

function onScrollGroupBuyPage(ev, instance) {
  var _dataset = ev.target.dataset;

  var titleInstance = instance.selectComponent('.header-title-wrap');
  var headBar = instance.selectComponent('.header-bar-wrap');
  var headBarIconWrap = instance.selectComponent('.header-icon-wrap');
  var newValue = Math.min(_dataset.height, ev.detail.scrollTop) / _dataset.height;
  var headStyles = {
    height: _dataset.height + 'PX',
    paddingTop: _dataset.paddingtop + 'PX',
    paddingRight: '120PX',
  };

  var iconWrapStyle = {
    opacity: 0,
  };

  var titleWrapStyle = {
    opacity: 1,
  };

  if (newValue > 0.6) {
    headStyles = {
      height: _dataset.height + 'PX',
      paddingTop: _dataset.paddingtop + 'PX',
      paddingRight: '120PX',
      background: "url('https://smart-static.wosaimg.com/themes/default/bgHeader.webp?x-oss-process=image/format,png')",
    };

    iconWrapStyle = {
      opacity: 1,
    };
    titleWrapStyle = {
      opacity: 0,
    };
  }

  headBar && headBar.setStyle(headStyles);
  titleInstance && titleInstance.setStyle(titleWrapStyle);
  headBarIconWrap && headBarIconWrap.setStyle(iconWrapStyle);
}

function getStoreItemDiscountTagClass(color) {
  if (!color) return '';
  else return ('color-' + color).toLowerCase();
}

function getContentPlanStyle(data, element = 'image', flg = true) {
  var style = '';
  switch (element) {
    case 'image':
      if (flg) {
        style += 'margin:0px 20rpx;width:fill-available;width:-webkit-fill-available;';
      } else {
        style += 'width:100%;';
      }
      break;
    case 'box':
      if (data.extra.bgColour) style += 'background-color:#' + data.extra.bgColour + ';';
      break;
  }

  return style;
}

function getMarkInfo(info) {
  // 热销的逻辑不改
  var ret = {
    style: '',
    title: '',
  };
  var current_own_category_id = info.current_own_category_id || '';
  var isHotSale = info.hotsale_product;

  var isProductTag = info.item_tags && info.item_tags.length;

  function setProductTagInfo() {
    var data = info.item_tags[0];
    ret.style = toStyle({
      background: data.tag_color_code,
      color: data.font_color_code,
    });
    ret.title = data.tag_name;
  }

  function setHotSaleInfo() {
    ret.title = '热销';
  }

  // 如果是热销，且不是标签商品，展示热销
  if (isHotSale && !isProductTag) {
    setHotSaleInfo();
  } else if (isProductTag && !isHotSale) {
    // 如果是标签商品，没有热销
    setProductTagInfo();
  } else if (isProductTag && isHotSale) {
    // 如果都有，则判断在哪个分类下

    // 在独立标签分类下
    if (current_own_category_id.indexOf('product_tag') !== -1) setProductTagInfo();
    // 在普通分类下和在热销分类下 展示热销
    else setHotSaleInfo();
  }

  return ret;
}

function getNexLine(info = '') {
  if (typeof info == 'string') {
    var bulletin_Arr = [];
    bulletin_Arr = info.split('\n');
    return bulletin_Arr;
  } else {
    return [];
  }
}

/**
 * 页面滑动时改变顶部颜色
 */
function onScrollCampusPage(ev, instance) {
  instance.callMethod('onScrollCampusPage', ev);
  var scrollTop = Math.floor(ev.detail.scrollTop);
  components = components || {
    headBarBg: instance.selectComponent('.header-bar-bg'),
  };
  var dataset = ev.target.dataset;
  var newValue = Math.min(dataset.height, scrollTop) / dataset.height;
  var headBarBg = components.headBarBg;
  if (newValue > 0.8) {
    headBarBg && headBarBg.addClass('header-bar-bg--pseudo');
  } else {
    headBarBg && headBarBg.removeClass('header-bar-bg--pseudo');
  }
}

module.exports = {
  getMarkInfo: getMarkInfo,
  bem: bem,
  addUnit: addUnit,
  when: when,
  isNil: isNil,
  isNumber: isNumber,
  isString: isString,
  isObject: isObject,
  isArray: isArray,
  max: max,
  min: min,
  abs: Math.abs,
  toArray: toArray,
  includes: includes,
  snakeCase: snakeCase,
  pxTransform: pxTransform,
  toStyle: toStyle,
  classNames: classNames,
  keys: keys,
  reduce: reduce,
  multiply: multiply,
  toYuan: toYuan,
  toDiscount: toDiscount,
  arrSize: arrSize,
  isPlainObject: isPlainObject,
  trackStyle: trackStyle,
  getHeaderBarStyle: getHeaderBarStyle,
  getHeaderSearchStyle: getHeaderSearchStyle,
  getHeaderSearchIconStyle: getHeaderSearchIconStyle,
  getCouponTagStyle: getCouponTagStyle,
  getGoodItemStyle: getGoodItemStyle,
  formatFloat: formatFloat,
  percentToDiscount: percentToDiscount,
  checkAttributeSelected: checkAttributeSelected,
  toEllipsis: toEllipsis,
  transTempToStr: transTempToStr,
  formatSaleCount: formatSaleCount,
  secondZhe: secondZhe,
  showMaxNum: showMaxNum,
  // propObserver: propObserver,
  onScrollHomePage: onScrollHomePage,
  onScrollCampusPage: onScrollCampusPage,
  getStoreItemDiscountTagClass: getStoreItemDiscountTagClass,
  getContentPlanStyle: getContentPlanStyle,
  onTapCategory: onTapCategory,
  assign: assignThemeStyle,
  onScrollGroupBuyPage: onScrollGroupBuyPage,
  getNexLine: getNexLine,
  // onCurrentCategoryId: onCurrentCategoryId
};
