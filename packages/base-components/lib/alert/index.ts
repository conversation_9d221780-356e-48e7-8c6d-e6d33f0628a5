import { PofeComponent, controller, isWeixin, props, watch } from '@wosai/pofe-runtime';
import parseAlipayHtml from './compiler-html.js';

@controller('alertComponent')
export class AlertComponent extends PofeComponent {
  @props
  alertVisible: boolean = false;

  @props
  title: string = '';

  @props
  content: string = '';
  @props
  ok: string = '';
  @props
  cancel = '';
  @props
  okColor = '';
  @props
  cancelColor = '';

  titleNode = '';
  contentNode = '';
  okNode = '';
  cancelNode = '';
  isWeixin = isWeixin();

  constructor() {
    super();
  }

  @watch('title,content,ok,cancel')
  onWatchPropsChange() {
    let map = ['title', 'content', 'ok', 'cancel'];
    let args = [].slice.call(arguments, 0);
    args.forEach((element: any, index: number) => {
      const key = map[index];

      if (isWeixin()) {
        this[`${key}Node`] = element;
      } else {
        this[`${key}Node`] = parseAlipayHtml(element).template.children;
      }
    });
  }

  handleClickAlertCancel() {
    this.$emit('alertCancel');
  }
  handleClickAlertOk() {
    this.$emit('alertOk');
  }
}
