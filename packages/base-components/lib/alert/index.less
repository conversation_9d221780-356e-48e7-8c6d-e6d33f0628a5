.alert-wrap {
  background-color: rgba(0, 0, 0, 0.5);
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
}

.alert {
  position: absolute;
  left: 14%;
  right: 14%;
  top: 35%;
  background-color: white;
  font-size: 32px;
  border-radius: 16px;
}

.alert-title {
  text-align: center;
  padding-top: 40px;
  font-weight: bold;
  padding-bottom: 10px;
}

.alert-line {
  width: 100%;
  height: 1px;
  background-color: #d7d7d7;
  transform: scaleY(0.5);
}

.alert-content {
  padding: 30px 40px;
  font-size: 28px;
  color: #8b909e;
}

.alert-footer {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 96px;
}

.alert-footer-divider {
  height: 100%;
  width: 1px;
  background-color: #d7d7d7;
  transform: scaleX(0.5);
}

.alert-footer-cancel {
  width: 100%;
  text-align: center;
  color: #9b9b9b;
}

.alert-footer-ok {
  width: 100%;
  text-align: center;
  color: #09bd04;
  &--my {
    color: rgba(22, 120, 227, 1);
  }
}
