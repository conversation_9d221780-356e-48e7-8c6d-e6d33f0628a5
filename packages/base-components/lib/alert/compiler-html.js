/**
 *
 *  <img style="width: 20px; vertical-align: middle;" src="https://images.wosaimg.com/0d/7af7ace….png" />
 * <span style="margin-left: 6px; line-heigght: 1; vertical-align: middle;">请确认付款信息</span>
 */

// Regular Expressions for parsing tags and attributes
var startTag =
    /^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,
  endTag = /^<\/([-A-Za-z0-9_]+)[^>]*>/,
  attr = /([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g;

// Special Elements (can contain anything)
const isPlainTextElement = makeMap('script,style');

const isSpecial = makeMap('script,style,template');

const shouldIgnoreFirstNewline = (html) => html[0] === '\n';

function parseHTML(html, handler) {
  var index,
    chars,
    match,
    lastTag,
    last = html;

  while (html) {
    chars = true;

    // Make sure we're not in a script or style element
    if (!isPlainTextElement[lastTag]) {
      if (shouldIgnoreFirstNewline(html) && !lastTag) {
        html = html.substring(1);
        continue;
      }
      // Comment
      if (html.indexOf('<!--') == 0) {
        index = html.indexOf('-->');

        if (index >= 0) {
          if (handler.comment) handler.comment(html.substring(4, index));
          html = html.substring(index + 3);
          chars = false;
        }

        // end tag
      } else if (html.indexOf('</') == 0) {
        match = html.match(endTag);

        if (match) {
          html = html.substring(match[0].length);
          match[0].replace(endTag, parseEndTag);
          chars = false;
        }

        // start tag
      } else if (html.indexOf('<') == 0) {
        match = html.match(startTag);

        if (match) {
          html = html.substring(match[0].length);
          match[0].replace(startTag, parseStartTag);
          chars = false;
        }
      }

      if (chars) {
        index = html.indexOf('<');
        var text = '';
        while (index === 0) {
          text += '<';
          html = html.substring(1);
          index = html.indexOf('<');
        }
        text += index < 0 ? html : html.substring(0, index);
        html = index < 0 ? '' : html.substring(index);

        if (handler.chars) handler.chars(text);
      }
    } else {
      html = html.replace(new RegExp('([\\s\\S]*?)</' + lastTag + '[^>]*>'), function (all, text) {
        text = text.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g, '$1$2');
        if (handler.chars) handler.chars(text);
        return '';
      });

      parseEndTag('', lastTag);
    }

    if (html == last) throw 'Parse Error: ' + html;
    last = html;
  }

  // Clean up any remaining tags
  parseEndTag();

  function parseStartTag(tag, tagName, rest, unary) {
    tagName = tagName.toLowerCase();
    var attrs = {};
    rest.replace(attr, function (match, name) {
      // fix scroll-y
      var value = arguments[2]
        ? arguments[2]
        : arguments[3]
        ? arguments[3]
        : arguments[4]
        ? arguments[4]
        : arguments[4];

      attrs[name] = value;
    });

    lastTag = tagName;

    unary = !!unary;
    if (handler.start) {
      handler.start(tagName, attrs, unary);
    }
  }

  function parseEndTag(tag, tagName) {
    if (handler.end) handler.end(tagName);
  }
}

function makeMap(str) {
  var obj = {},
    items = str.split(',');
  for (var i = 0; i < items.length; i++) obj[items[i].trim()] = true;
  return obj;
}

export default function parseAlipayHtml(template) {
  if (typeof template !== 'string') return { template: { chidlren: [] } };
  template = `<template>${template}</template>`;
  template = template.trim();
  let stack = [];
  let root = {};
  let currentParent;
  parseHTML(template, {
    start: function start(tag, attrs, unary) {
      let element = {
        name: tag,
        attrs,
        children: [],
      };
      if (isSpecial[tag]) {
        root[tag] = element;
      } else {
        currentParent.children.push(element);
      }

      // 没有tag结束符
      if (!unary) {
        currentParent = element;
        stack.push(element);
      }
    },

    end: function end() {
      let element = stack[stack.length - 1];
      if (element) {
        let lastNode = element.children[element.children.length - 1];
        if (lastNode && lastNode.type === 3 && lastNode.text === ' ') {
          element.children.pop();
        }
        // pop stack
        stack.pop();
        currentParent = stack[stack.length - 1];
      }
    },

    chars: function chars(text) {
      if (currentParent) {
        let children = currentParent.children;
        let el = {
          type: 'text',
          text: text,
        };
        children.push(el);
      }
    },
    comment: function comment(text) {},
  });
  return root;
}
