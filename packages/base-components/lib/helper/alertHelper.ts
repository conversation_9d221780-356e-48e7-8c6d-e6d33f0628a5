export type IAlertType = 'CONFIRM' | 'CLOSE_WINDOW' | 'RELOAD_PAGE' | 'CLOSE' | 'OUT_LINK';

export type IAlertBaseParams = {
  message: string; // 内容文案
  title: string; // 标题文案
  ok: string; // 确认按钮文案
  cancel: string; // 取消按钮文案
};
export interface IAlertServerParams extends IAlertBaseParams {
  type: IAlertType;
  onOk: {
    type: IAlertType;
  };

  onCancel: {
    type: IAlertType;
  };
}

export interface IAlertClientParams extends Partial<IAlertBaseParams> {
  onOk?: Function;
  onCancel?: Function;
}

let defaultParams = {
  message: '付款失败',
  title: '温馨提示',
  ok: '我知道了',
};

let extendMethods = {
  alertOnOk() {
    let { alert = {} } = this;

    if (alert.onOk) alert.onOk.call(this);
    else this.closeAlert();
  },

  alertOnCancel() {
    let { alert = {} } = this;

    if (alert.onCancel) alert.onCancel.call(this);
    else this.closeAlert();
  },

  setAlert(params: IAlertClientParams) {
    this.alert = {
      visible: true,
      title: params.title,
      content: params.message,
      ok: params.ok,
      cancel: params.cancel,
      onOk: params.onOk || this.closeAlert,
      onCancel: params.onCancel || this.closeAlert,
    };
  },

  // 展示alert
  openAlert(p: IAlertClientParams = {}) {
    let params = Object.assign({}, defaultParams, p);
    delete params.cancel;
    this.setAlert(params);
  },
  // 取消alert
  closeAlert: function () {
    console.log('closeAlert');
    this.alert = {};
  },

  openConfirm(p: IAlertClientParams = {}) {
    let params = Object.assign({}, defaultParams, { cancel: '取消' }, p);

    this.setAlert(params);
  },
  closeConfirm: function () {
    console.log(`close confirm`);
    this.closeAlert();
  },

  /**
     * 
     * @param data 
     *   "action": {
                "type": "CONFIRM",
                "message": "该终端仅支持在中国境内扫码收款，不支持跨境交易（EP189）",
                "title": "提示",
                "onOk": {
                    "type": "CLOSE_WINDOW"
                },
                "onCancel": {
                    "type": "RELOAD_PAGE"
                },
                "ok": "退出",
                "cancel": "取消"
            },
     */

  processRisk(data: { action?: IAlertServerParams }) {
    try {
      if (!data || !data.action) throw new Error('');
      let { action } = data;

      let { type, title, message, ok, cancel, onOk, onCancel } = action;

      const getRiskProcessorForType = (type: IAlertType) => {
        let matches = [
          {
            type: 'CONFIRM',
            do: () => {
              let params: IAlertClientParams = {
                title,
                message,
                ok,
                cancel,
                onOk: () => {
                  let processor = getRiskProcessorForType(onOk.type);
                  processor.call(this);
                },
                onCancel: () => {
                  let processor = getRiskProcessorForType(onCancel.type);
                  processor.call(this);
                },
              };

              this.openConfirm(params);
            },
          },
          {
            type: 'CLOSE_WINDOW',
            do: this.closeAlert,
          },
          {
            type: 'RELOAD_PAGE',
            do: this.closeAlert,
          },
          {
            type: 'CLOSE',
            do: this.closeAlert,
          },
        ];

        for (let i = 0; i < matches.length; i++) {
          if (matches[i].type === type) return matches[i].do;
        }

        return this.closeAlert;
      };

      let processor = getRiskProcessorForType(type);
      processor.call(this);
    } catch (error) {
      throw new Error('');
    }
  },
};

export function injectAlertHelper(target: any) {
  let prototype = target.prototype;

  let keys = Object.keys(extendMethods);

  keys.forEach((key) => {
    prototype[key] = extendMethods[key];
  });
}
