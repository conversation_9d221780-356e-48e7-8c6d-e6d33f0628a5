import _ from '@wosai/emenu-mini-lodash';

import { PofeComponent, controller, props, watch } from '@wosai/pofe-runtime';

import { HelperService } from '@wosai/pofe-design';

/**
 * @example
 * <sqb-icon name="phone" style="color:orange;font-size:20px;" bind:click="onClick"/>
 */

@controller('baseComponentForIcon')
class BaseComponentForIcon extends PofeComponent {
  helperService: HelperService;

  @props
  customStyle = '';

  @props
  name = null;

  @props
  width = null;

  @props
  height = null;

  @props
  size = null;

  @props
  color = null;

  @props
  bgColor = null;

  @props
  borderColor = null;

  @props
  radius = null;

  @props
  rotate = null;

  @props
  border = null;

  @props
  disabled = false;

  @props
  isTrigger = true;
  @props
  index = null;

  @props
  className = '';

  @props
  lineHeight = null;

  iconStyles = '';
  borderStyles = '';

  constructor({ helperService }) {
    super();
    this.helperService = helperService;
  }

  onClick() {
    this.$emit('click', { index: this.index });
  }

  @watch('width, height, size, color, bgColor, border, rotate, radius, customStyle, lineHeight, borderColor')
  onWatchIconChange(...props: any) {
    let { helperService } = this;
    const [
      width,
      height,
      fontSize,
      color,
      backgroundColor,
      border,
      rotate,
      borderRadius,
      customStyle,
      lineHeight,
      borderColor,
    ] = props;
    let iconStyles = _.assign(
      helperService.styleParse(customStyle),
      _.omitBy(
        {
          fontSize,
          color,
          backgroundColor,
          borderRadius,
          width,
          height: height || width,
          lineHeight: lineHeight || height || width,
          transform: `rotate(${rotate || 0}deg)`,
        },
        _.isNull,
      ),
    );
    iconStyles = helperService.styleStringify(iconStyles);
    let borderStyles = null;
    if (border) {
      const borderWeight = helperService.pxTransform(border);
      borderStyles = helperService.styleStringify({
        border: `${borderWeight} solid ${borderColor || color}`,
        borderRadius,
      });
    }
    this.iconStyles = iconStyles;
    this.borderStyles = borderStyles;
  }
}
