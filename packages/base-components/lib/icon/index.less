/* CDN 服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: 'iconfont'; /* Project id 2336711 */
  src: url('https://at.alicdn.com/t/c/font_2336711_t6g2wut1wq.woff2?t=1661493822778') format('woff2'),
    url('https://at.alicdn.com/t/c/font_2336711_t6g2wut1wq.woff?t=1661493822778') format('woff'),
    url('https://at.alicdn.com/t/c/font_2336711_t6g2wut1wq.ttf?t=1661493822778') format('truetype');
}

.sqb-icon {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.sqb-icon-border {
  box-sizing: border-box;
  pointer-events: none;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
}

.x-icon-prefix {
  font-family: 'iconfont' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: flex;
  justify-content: center;
  align-items: center;
}

.x-icon-backspace:before {
  content: '\e6a7';
}

.x-icon-red-packet:before {
  content: '\e6a6';
}

.x-icon-minus:before {
  content: '\e697';
}

.x-icon-minus-round:before {
  content: '\e60e';
}

.x-icon-creditcard:before {
  content: '\e690';
}

.x-icon-arrow:before {
  content: '\e691';
}

.x-icon-wechat:before {
  content: '\e692';
}

.x-icon-cart:before {
  content: '\e693';
}

.x-icon-alipay:before {
  content: '\e694';
}

.x-icon-note:before {
  content: '\e619';
}

.x-icon-phone-brief:before {
  content: '\e696';
}

.x-icon-search:before {
  content: '\e61a';
}

.x-icon-switch:before {
  content: '\e699';
}

.x-icon-tick:before {
  content: '\e62b';
}

.x-icon-tickMcDonald:before {
  content: '\e69a';
}

.x-icon-trash:before {
  content: '\e69c';
}

.x-icon-order-pending:before {
  content: '\e69d';
}

.x-icon-plus:before {
  content: '\e69e';
}

.x-icon-plus-round:before {
  content: '\e60d';
}

.x-icon-share:before {
  content: '\e69f';
}

.x-icon-order-fail:before {
  content: '\e6a0';
}

.x-icon-phone:before {
  content: '\e6a1';
}

.x-icon-praise:before {
  content: '\e6a2';
}

.x-icon-write:before {
  content: '\e6a3';
}

.x-icon-order-cancel:before {
  content: '\e6a4';
}

.x-icon-del:before {
  content: '\e654';
}

.x-icon-close:before {
  content: '\e656';
}

.x-icon-yilingqu:before {
  content: '\e657';
}

.x-icon-close:before {
  content: '\e656';
}

.x-icon-yilingqu:before {
  content: '\e657';
}

.x-icon-close:before {
  content: '\e656';
}

.x-icon-yilingqu:before {
  content: '\e657';
}

.x-icon-recommend:before {
  content: '\e601';
}

.x-icon-hotSale:before {
  content: '\e602';
}

.x-icon-arrow-line:before {
  content: '\e603';
}

.x-icon-marker:before {
  content: '\e600';
}

.x-icon-star:before {
  content: '\e60f';
}

.x-icon-star-fill:before {
  content: '\e610';
}

.x-icon-msg:before {
  content: '\e658';
}

.x-icon-store:before {
  content: '\e629';
}

.x-icon-order:before {
  content: '\e78a';
}

.x-icon-like:before {
  content: '\e813';
}

.x-icon-coupon:before {
  content: '\e669';
}

.x-icon-message:before {
  content: '\e63c';
}

.x-icon-wechat-line:before {
  content: '\e604';
}

.x-icon-logo:before {
  content: '\e66b';
}

.x-icon-like-fill:before {
  content: '\e670';
}
.x-icon-industry:before {
  content: '\e60b';
}

.x-icon-check:before {
  content: '\e60a';
}

.x-icon-medal:before {
  content: '\e605';
}

.x-icon-point:before {
  content: '\e607';
}

.x-icon-clock:before {
  content: '\e608';
}

.x-icon-distribution:before {
  content: '\e60c';
}

.x-icon-love:before {
  content: '\e61d';
}
.x-icon-love-line:before {
  content: '\e622';
}

.x-icon-notice:before {
  content: '\e609';
}

.x-icon-again:before {
  content: '\e65a';
}

.x-icon-merchant:before {
  content: '\e65b';
}

.x-icon-rider:before {
  content: '\e65c';
}

.x-icon-service:before {
  content: '\e65d';
}

.x-icon-point-line:before {
  content: '\e613';
}

.x-icon-clock-line:before {
  content: '\e611';
}

.x-icon-love-un:before {
  content: '\e62d;';
}

.x-icon-question:before {
  content: '\e62f';
}

.x-icon-close-circle:before {
  content: '\e616';
}

.x-icon-weixin-circle:before {
  content: '\e614';
}
.x-icon-lower-triangle::before {
  content: '\e642';
}
