<wxs src="../wxs/utils.wxs" module="utils" />

<view class="{{className}} {{utils.classNames('sqb-icon', {disabled})}}" style="{{customStyle}}">
  <!-- isTrigger用于判断返回键是否有返回功能 -->
  <view
    style="{{iconStyles}}"
    class="x-icon-{{name}} x-icon-prefix"
    data-event-name="click, tap"
    catchtap="onClick"
    wx:if="{{isTrigger}}"
  />
  <view
    style="{{iconStyles}}"
    class="x-icon-{{name}} x-icon-prefix"
    wx:else
  />
  <view
    wx:if="{{border}}"
    style="{{borderStyles}}"
    class="sqb-icon-border"
  />
  <slot/>
</view>

