{"name": "@wosai/emenu-mini-domainreduce", "version": "6.7.10-alpha.0", "description": "> TODO: description", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "http://git.wosai-inc.com:MK/emenu-mini-core", "maintainers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "license": "ISC", "main": "dist/index.js", "types": "dist/index.d.ts", "tsConfig": {"declaration": true, "emitDeclarationOnly": false, "declarationMap": false}, "external": ["@wosai/emenu-mini-domaincommon", "@wosai/emenu-mini-utils", "@wosai/emenu-mini-config", "@wosai/emenu-mini-store", "@wosai/emenu-mini-di"], "entry": "lib/index.ts", "directories": {"lib": "lib"}, "files": ["lib", "dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "*********************:MK/emenu-mini-core.git"}, "scripts": {"build": "rollup -c ../../rollup.config.js", "dev": "rollup -c ../../rollup.config.js -w"}, "devDependencies": {"@wosai/emenu-mini-di": "1.0.0", "@wosai/emenu-mini-domaincommon": "^6.7.10-alpha.0", "@wosai/emenu-mini-utils": "6.63.1-alpha.0", "@wosai/emenu-mini-config": "6.63.5-alpha.0"}, "gitHead": "f24db26f676901bb8625ccf77b945f1d31f698d1"}