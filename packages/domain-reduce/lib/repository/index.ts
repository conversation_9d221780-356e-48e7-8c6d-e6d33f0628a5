//@ts-ignore
import { StorageUtils } from '@wosai/emenu-mini-utils';

//@ts-ignore
import { getLocalTheme, getThemeConfig } from '@wosai/emenu-mini-store/lib/index.js';

let { storage } = StorageUtils;
export interface IStore {
  merchantId: string;
  merchantName: string;
  merchantSn: string;
  storeExtraName: string;
  storeId: string;
  storeName: string;
  storeSn: string;
}

export interface IUser {
  cellphone: string;
  externalSource: string;
  thirdpartyUserId: string;
  token: string;
  unionId: string;
  userId: string;
  userInfo: {
    avatarUrl: string;
    city: string;
    country: string;
    gender: number;
    nickName: string;
    province: string;
    refUserId: string;
    type: string;
    typeId: string;
  };
}

export class ReduceRepository {
  store: IStore = null;
  user: IUser = null;
  config: any = null; //主体配置文件
  constructor() {
    this.store = null;
    this.user = null;
    this.config = getLocalTheme();
  }

  setUser(value: IUser) {
    // if (value && value.token) {
    //   //@ts-ignore
    //   storage('token', value.token);
    // }
    this.user = value;
  }

  getUser() {
    return this.user;
  }

  setConfig(value: any) {}

  getConfig() {
    return this.config;
  }

  setStore(value: IStore) {
    this.store = value;
  }

  getStore() {
    return this.store;
  }
}
