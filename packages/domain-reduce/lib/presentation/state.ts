import { IStore, IUser } from '../repository';

export enum REDUCESTATE {
  INIT = 'INIT',
  UPDATEUSER = 'UPDATEUSER', // user更新
  UPDATESTORE = 'UPDATESTORE', // store更新
}

export interface REDUCEINITSTATE {
  kind: REDUCESTATE.INIT;
}

export interface USERUPDATEDSTATE {
  kind: REDUCESTATE.UPDATEUSER;
  value: IUser;
}

export interface STOREUPDATEDSTATE {
  kind: REDUCESTATE.UPDATESTORE;
  value: IStore;
}

export type ReduceState = USERUPDATEDSTATE | STOREUPDATEDSTATE | REDUCEINITSTATE;

export const initalReduceState: ReduceState = {
  kind: REDUCESTATE.INIT,
};
