import { DOMAINPLUGINNAME, Ploc } from '@wosai/emenu-mini-domaincommon';
import { IStore, IUser, ReduceRepository } from '../repository';
import { initalReduceState, ReduceState, REDUCESTATE } from './state';
import { asClass } from '@wosai/emenu-mini-di';
import { StorageUtils } from '@wosai/emenu-mini-utils';
import  Constants from '@wosai/emenu-mini-config';

const { storage } = StorageUtils;

let { AUTH_EXPIRE_TIME } = Constants;

export class ReducePloc extends Ploc<ReduceState> {
  name: string = null;
  reduceRepository: ReduceRepository = null;
  constructor({ reduceRepository }) {
    super(initalReduceState);
    this.name = DOMAINPLUGINNAME.REDUCE;
    this.reduceRepository = reduceRepository;
  }

  setUser(data: IUser) {
    this.reduceRepository.setUser(data);
    this.changeState({
      kind: REDUCESTATE.UPDATEUSER,
      value: this.getUser(),
    });
  }

  setStore(data: IStore) {
    this.reduceRepository.setStore(data);
    this.changeState({
      kind: REDUCESTATE.UPDATESTORE,
      value: this.getStore(),
    });
  }

  getStore(): IStore {
    return this.reduceRepository.getStore();
  }

  getUser(): IUser {
    return this.reduceRepository.getUser();
  }

  setConfig(data: any) {
    return this.reduceRepository.setConfig(data);
  }

  getConfig() {
    return this.reduceRepository.getConfig();
  }

  //key:authPhone/auth
  checkAuthedInExpire(key = 'authPhone') {
    //@ts-ignore
    const updateAt = storage(`${key}.updateAt`);
    let cycle = AUTH_EXPIRE_TIME;
    // 24 小时不再弹起授权
    if (updateAt && updateAt + cycle * 1000 > Date.now()) {
      return true;
    }
    return false;
  }

  // 是否需要授权用户信息
  async checkShouldAuthUer(ctx) {
    let { sqbBridge } = ctx.data;

    try {
      let expire = this.checkAuthedInExpire('auth');
      console.log(`###auth expire is `, expire);
      if (expire) return false; //在24小时内
      let user = await sqbBridge.getMiniProgramUser();

      console.log(`###auth user is `, user);
      if (user.avatarUrl) return false; //授过权了

      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  markUserAuthTime() {
    storage(`auth`, { updateAt: Date.now() });
  }

  // 神策需要的store属性
  getStoreProprtties() {
    try {
      let store: Record<any, any> = this.reduceRepository.getStore() || {};
      let {
        merchantId,
        merchantName,
        merchantSn,
        storeId,
        storeName,
        storeSn,
        organizationPath,
        storeAddress = {},
      } = store;

      let { province, city, district } = storeAddress;
      return {
        merchant_id: merchantId,
        merchant_sn: merchantSn,
        merchant_name: merchantName,
        store_id: storeId,
        store_sn: storeSn,
        store_name: storeName,
        //store_org_id:"",
        store_org_path: organizationPath,
        province,
        city,
        district,
      };
    } catch (err) {
      console.log(err);
      return {};
    }
  }
}

const makeReduceModule = ({ container }) => {
  container.register({
    reduceRepository: asClass(ReduceRepository),
    reducePloc: asClass(ReducePloc),
  });
};

export { makeReduceModule };
