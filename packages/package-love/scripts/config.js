const glob = require('glob');
const Path = require('path');
const relative = require('relative');

function getEntry(rootSrc, rel, opts = {}) {
  var map = {};
  // glob.sync(rootSrc + rel, opts).forEach((file) => {
  glob.sync(`${rootSrc}${rel}`, opts).forEach((file) => {
    var key = relative(rootSrc, file);
    map[key] = file;
  });
  return map;
}

const srcPath = Path.resolve(__dirname, '../lib');
let opts = {};
let component = getEntry(srcPath, '/**/*.{j,t}s', opts);
let entry = Object.assign({}, component);

module.exports = {
  entry,
  dist: Path.resolve(__dirname, '../dist'),
  resolve: {
    alias: {
      '@services': Path.resolve(__dirname, '../lib/services'),
    },
  },
  externals: (path) => {
    let reg = /^@wosai\//;
    let baseComponentReg = /^@npm-base-components/;
    if (reg.test(path)) return true;
    if (baseComponentReg.test(path)) return true;
    return false;
  },
};
