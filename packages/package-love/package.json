{"name": "@wosai/emenu-mini-package-love", "description": "", "version": "1.0.1", "scripts": {"build": "node scripts/build.js", "dev": "rollup -c ../../rollup.config.js -w"}, "files": ["dist"], "miniprogram": "dist", "entry": "lib/index.ts", "types": "dist/index.d.ts", "dependencies": {}, "devDependencies": {"@types/node": "^18.7.14", "@wosai/pofe-compiler": "1.9.0", "@wosai/pofe-runtime": "1.9.0", "@wosai/pofe-native-api": "1.9.0", "@wosai/pofe-design": "1.9.0", "@wosai/emenu-mini-base-components": "1.0.0", "@wosai/emenu-mini-dayjs": "6.0.25", "@wosai/emenu-mini-lodash": "6.0.25", "@babel/plugin-transform-arrow-functions": "^7.18.6"}}