// const apis = require('@tarojs/taro-h5/dist/taroApis');

module.exports = function (api) {
  api.cache(true);

  const presets = [
    [
      '@babel/preset-env',
      {
        spec: true,
        useBuiltIns: false,
        modules: false,
        targets: {
          node: 'current',
        },
      },
      // {
      //   targets: {
      //     node: '8.9',
      //   },
      // },
    ],
  ];

  // 非本地调试模式才压缩代码，不然调试看不到实际变量名
  // if (!process.env.LOCAL_DEBUG) {
  //   presets.push(['minify']);
  // }

  const plugins = [
    ['@babel/plugin-proposal-decorators', { legacy: true }],
    ['@babel/plugin-proposal-class-properties'],
    // [
    //   'babel-plugin-transform-taroapi',
    //   {
    //     apis,
    //     packageName: '@tarojs/taro-h5',
    //   },
    // ],
    [
      '@babel/plugin-transform-runtime',
      {
        regenerator: true,
        helpers: true,
      },
    ],
  ];

  return {
    presets,
    plugins,
    parserOpts: {
      allowReturnOutsideFunction: true,
    },
    env: {
      test: {
        plugins: ['@babel/plugin-transform-modules-commonjs'],
        presets: ['jest'],
      },
    },
    ignore: ['node_modules'],
  };
};
