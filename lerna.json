{"packages": ["packages/*"], "command": {"bootstrap": {"hoist": true}, "version": {"conventionalCommits": true}, "publish": {"registry": "https://registry-npm.wosai-inc.com/", "allowBranch": ["master", "feature/*", "release/*"], "conventionalCommits": true, "ignoreChanges": ["lerna.json", "babel.config.js", "rollup.config.js", ".npmrc", "package-lock.json", "**/*.test.js"]}}, "ignoreChanges": ["**/*.md", "packages/*/CHANGELOG.md", "lerna.json", "babel.config.js", "rollup.config.js", ".npmrc", "package-lock.json", "**/*.test.js"], "version": "7.82.0"}